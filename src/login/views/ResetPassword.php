<!-- Reset Password Page -->
<div class="reset-password">
    <div class="reset-password-wrapper">
        <form name="frmReminder" action="<?php echo $this->scripturl; ?>?action=reset_password2" method="post">
            <div class="reset-password-container">

                <!-- Title -->
                <div class="reset-title">
                    <?php echo _tk('reset_password_title'); ?>
                </div>

                <!-- Error or Info Message -->
                <?php if ($this->error != '') : ?>
                <div class="error-message-container">
                    <div class="reset-error-message">
                        <?php echo \src\security\Escaper::escapeForHTML($this->error); ?>
                    </div>
                </div>
                <?php else : ?>
                <div class="reset-info-container">
                    <div class="reset-info-message">
                        <?php echo _tk('reset_instructions'); ?>
                    </div>
                </div>
                <?php endif; ?>

               <!-- Username Input with Inline Error Icon -->
                <div class="form-control">
                    <label for="username" class="form-label-text">
                        <?php echo ucfirst(strtolower(str_replace(' ', '', _tk('user_name')))); ?>
                    </label>
                     <!-- Input wrapper for positioning error icon inside input -->
                    <div class="input-icon-wrapper">
                        <input type="text"
                            class="form-input-box <?php echo (!empty($this->usernameError)) ? 'error' : ''; ?>"
                            id="username" name="username" maxlength="254" size="30"
                            value="<?php echo Escape::EscapeEntities($this->user); ?>" placeholder="Username" />
                        <?php if (!empty($this->usernameError)) : ?>
                        <!-- Inline SVG icon shown when username has an error -->    
                        <div class="input-error-icon" aria-hidden="true">
                            <div class="input-error-icon-img"></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Captcha Input with Inline Error Icon -->
                <div class="form-control">
                    <label for="captcha" class="form-label-text">
                        <?php echo _tk('captcha_code'); ?>
                    </label>
                     <!-- Wrapper for input + error icon -->
                    <div class="input-icon-wrapper">
                        <input type="text"
                            class="form-input-box <?php echo (!empty($this->captchaError)) ? 'error' : ''; ?>"
                            id="captcha" name="captcha" maxlength="10" size="10" placeholder="Captcha" />
                        <?php if (!empty($this->captchaError)) : ?>
                         <!-- Error icon when captcha input is invalid -->   
                        <div class="input-error-icon" aria-hidden="true">
                             <div class="input-error-icon-img"></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Captcha Image -->
                <div class="form-control">
                    <img src="Source/libs/captcha.php" width="100%" />
                </div>

                <!-- Submit Button -->
                <div class="form-control-request-password-change">
                    <input class="form-submit-button-password" type="submit"
                        value="<?php echo _tk('btn_reset_password'); ?>" name="verify" />
                </div>

            </div>
        </form>
    </div>
</div>