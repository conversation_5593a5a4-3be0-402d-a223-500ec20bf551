<div id="login" style="float: left;">
    <div class="login-wrap">
        <form id="frmLogin" name="frmLogin"
            action="<?php echo $this->scripturl; ?>?action=login2<?php echo $this->form_idtag; ?>" method="post">
            <input type="hidden" name="url" value="<?php echo $this->url; ?>" />
            <input type="hidden" name="form_id" value="<?php echo Sanitize::SanitizeInt($this->formID); ?>" />
            <?php
            $showLogin = true;
            $authType = GetParm("AUTH_TYPE", 'LOCAL', true);
            if($authType == 'EXTERNAL') {  
                    $showLogin = false;
            }
            ?>

            <?php if($showLogin) :?>
            <div class="login-table-container">
                <div class="login_form_table">
                    <div class="windowbg">
                        <div class="login_form_inner_table">
                            <div class="titlebg">

                                <h1><?php echo htmlspecialchars(_tk('login_title')); ?></h1>

                                <?php if ($this->login_failed != '') : ?>
                                <div class="windowbg2">
                                    <!-- Flex wrapper to align error message and help icon horizontally .Margin class switches between 16px and 8px on help icon toggle -->
                                    <div class="login-error-wrapper login-error-margin-lg"
                                        style="display:flex; margin-top: 8px;">
                                        <!-- Main login error message ("Login attempt unsuccessful.") -->
                                        <span class="login-error-box"><?php echo _tk('login_failed_err'); ?></span>
                                        <!-- Toggles detailed error message visibility and adjusts margin spacing between main and detail -->
                                        <img src="Images/Help.svg" class="basic_link_csr_js login-error-help-icon"
                                            jsCall="jQuery('#login_failed_desc').toggle(); jQuery('.login-error-wrapper').toggleClass('login-error-margin-sm')" />
                                    </div>
                                    <div id="login_failed_desc" class="login-error-extra-text" style="display:none">
                                        <?php echo _tk('login_failed_err_desc'); ?>
                                    </div>
                                </div>
                                <?php elseif ($this->error != '') : ?>
                                <div class="windowbg2 login-error-wrapper login-error-margin-lg"
                                    style="margin-top: 8px;">
                                    <!-- Main login error message ("Login attempt unsuccessful.") -->
                                    <span class="login-error-box">
                                        <?php echo src\security\Escaper::escapeForHTML($this->error); ?>
                                    </span>
                                    <?php  if ($this->attempts && $this->attempts > 0) :  ?>
                                    <!-- Displays remaining login attempts below the error message.Adds top margin (8px) to separate it from the error box above -->
                                    <span class="login-error-extra-text login-error-margin-top-sm">
                                        <?php echo src\security\Escaper::escapeForHTML(_tk('login_attempts_left') . ': ' . $this->attempts); ?>
                                    </span>
                                    <?php endif; ?>
                                </div>
                                <?php endif;
                         unset($_SESSION['login_error_message']);
                        ?>
                            </div>
                            <!-- Username Field -->
                            <div class="login_form_control">
                                <label for="name" class="login_label_text">
                                    <?php echo ucfirst(strtolower(str_replace(' ', '', $this->usernameLabel))); ?></label>
                                    <!-- Applies 'error' class if login failed and username is empty -->
                                      <input type="text" class="login_input_box <?php 
                                            echo (isset($this->login_failed ) && (empty($this->user))) ? 'error' : ''; 
                                        ?>"  id="name" name="user" size="20"
                                         maxlength="254" value="<?php echo $this->user; ?>" autocomplete="new-password" />
                            </div>
                            <!-- Password Field -->
                            <div class="login_form_control">
                                <label for="password" class="login_label_text"><?php echo $this->phraseLabel; ?></label>
                                 <!-- Applies 'error' class if login failed and password is empty -->
                               <input type="password" class="login_input_box <?php  echo isset($this->login_failed) ? 'error' : ''; ?>"   id="password" name="passwrd" size="20"
                                autocomplete="new-password" />
                            </div>
                            <!-- Domain Dropdown -->
                            <?php if ($this->LDAPAuthentication && getCount($this->ldap_domains) > 0) : ?>
                            <div class="form_control-dropdown">
                                <label for="domain" class="login_label_text"><?php echo _tk("domain"); ?></label>
                                <!-- Outer wrapper for custom dropdown styling -->
                               <div class="custom-select-wrapper">
                                    <div class="login_select_box" tabindex="0">
                                          <span class="selected-option"><?php echo $this->selectedDomain ?: '&lt;' . _tk('domain_none_option') . '&gt;'; ?></span>
                            
                                        <ul class="custom-options">
                                        <li data-value=""><?php echo '&lt;' . _tk('domain_none_option') . '&gt;'; ?></li>
                                        <?php foreach ($this->ldap_domains as $ldapDomain): ?>
                                            <li data-value="<?php echo $ldapDomain->recordid; ?>"
                                                class="<?php echo $ldapDomain->domain == $this->selectedDomain ? 'selected' : ''; ?>">
                                            <?php echo $ldapDomain->domain; ?>
                                            </li>                                          
                                        <?php endforeach; ?>
                                        </ul>
                                        <input type="hidden" name="domain" id="domain" value="<?php echo $this->selectedDomain; ?>">
                                    </div>
                                    </div>
                                        </div>
                            <?php endif; ?>
                            <?php
                        // Determine the wrapper class based on LDAP authentication status. If LDAP is enabled → show forgot password and login button (spread layout)
                        // Else → show only login button (centered layout).
                        $forgot_Password_Class = (!$this->LDAPAuthentication)  ? 'login-container-with-forgot'  : 'login-container-center-only'; ?>
                            <!-- Submit Button Wrapper .Class will vary depending on LDAP status -->
                            <div class="<?php echo $forgot_Password_Class; ?>">
                                <?php if (!$this->LDAPAuthentication) : ?>
                                <div class="windowbg2 forgot-password-wrapper">
                                    <!-- Forgot Password Link -->
                                    <a class="basic_link_csr_js thisLinkCsr"
                                        jsCall="jQuery('#frmLogin').attr('action', 'index.php?action=reset_password').submit();">
                                        <small><?php echo _tk('forgotten_password'); ?></small>
                                    </a>
                                </div>
                                <?php endif; ?>
                                <input class="submit_button" type="submit"
                                    value="<?php echo str_replace(' ', '', _tk('btn_log_in')); ?>" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <?php else: ?>
    <table border="0" width="400" cellspacing="1" cellpadding="0" class="bordercolor" align="center">
        <tr>Single Sign-On (SSO) is currently enabled for this application. Please log in using <a
                href="<?php echo $this->scripturl; ?>?action=login&amp;method=sso" tabindex="3">SSO Login</a>.</tr>
    </table>
    <?php endif;?>
    </form>
</div>
</div>
<?php if (!empty($this->userImage)): ?>
<div style="float: left; padding: 15px;">
    <?php echo $this->userImage; ?>
</div>
<?php endif; ?>
<script language="javascript" type="text/javascript" nonce="<?php echo $_SESSION['nonce']; ?>">
document.frmLogin.user.focus();
/*This script enables a custom dropdown behavior for the domain selector on the login screen.It replaces the native <select> with a stylized <ul><li>-based dropdown.
Functionality includes open/close toggle, item selection, value assignment, and accessibility behavior.*/
document.addEventListener('DOMContentLoaded', function () {
  const dropdown = document.querySelector('.login_select_box'); // Main dropdown container
  const selected = dropdown.querySelector('.selected-option');  // Element displaying selected text
  const hiddenInput = dropdown.querySelector('input[type="hidden"]'); // Hidden input to store selected value
  const options = dropdown.querySelectorAll('.custom-options li'); // All <li> dropdown options

  let focusIndex = -1; // Tracks currently keyboard-focused option

  // Make options keyboard-focusable and track initially selected option
  options.forEach((li, index) => {
    li.setAttribute('tabindex', '0');
    if (li.classList.contains('selected')) {
      focusIndex = index;
    }
  });

  // Toggle dropdown open/close on mouse click
  dropdown.addEventListener('click', function (e) {
    dropdown.classList.toggle('open');
  });

  // Handle keyboard controls for dropdown
  dropdown.addEventListener('keydown', function (e) {

    // Open dropdown when Enter or Space is pressed
    if ((e.key === 'Enter' || e.key === ' ') && !dropdown.classList.contains('open')) {
      e.preventDefault();
      dropdown.classList.add('open');
      return;
    }

    // Skip if dropdown is closed
    if (!dropdown.classList.contains('open')) return;

    // Navigate down the list with ArrowDown
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      focusIndex = (focusIndex + 1) % options.length;
      moveFocus(options, focusIndex);
    }

    // Navigate up the list with ArrowUp
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      focusIndex = (focusIndex - 1 + options.length) % options.length;
      moveFocus(options, focusIndex);
    }

    // Close dropdown on Escape key
    if (e.key === 'Escape') {
      dropdown.classList.remove('open');
    }

    // Select currently focused item on Enter
    if (e.key === 'Enter' && focusIndex >= 0) {
      e.preventDefault();
      setSelected(options[focusIndex]);
      dropdown.classList.remove('open');
    }
  });

  // Handle mouse selection of options
  options.forEach((option, index) => {
    option.addEventListener('click', function (e) {
      e.stopPropagation();
      setSelected(option);
      dropdown.classList.remove('open');
    });

    // Apply .focus class when focused
    option.addEventListener('focus', function () {
      removeFocusClass();
      this.classList.add('focus');
      focusIndex = index;
    });

    // Remove .focus class on blur
    option.addEventListener('blur', function () {
      this.classList.remove('focus');
    });
  });

  // Close dropdown if clicked outside
  document.addEventListener('click', function (e) {
    if (!dropdown.contains(e.target)) {
      dropdown.classList.remove('open');
    }
  });

  // Extra Escape handling (safety fallback)
  document.addEventListener('keydown', function (e) {
    if (e.key === 'Escape') {
      dropdown.classList.remove('open');
    }
  });

  // === Helper Functions ===

  // Set selected item and update hidden input
  function setSelected(option) {
    const value = option.getAttribute('data-value');
    const label = option.textContent;

    selected.textContent = label;
    hiddenInput.value = value;

    options.forEach(opt => opt.classList.remove('selected'));
    option.classList.add('selected');
  }

  // Move focus to a specific option and apply .focus styling
  function moveFocus(options, index) {
    removeFocusClass();
    options[index].focus();
    options[index].classList.add('focus');
    options[index].scrollIntoView({ block: 'nearest' });
  }

  // Remove .focus class from all options
  function removeFocusClass() {
    options.forEach(opt => opt.classList.remove('focus'));
  }
});
</script>