<?php

namespace src\login\controllers;

use src\email\EmailSenderFactory;
use src\framework\controller\TemplateController;
use src\security\PasswordHasher;
use src\users\model\UserModelFactory;

class ResetPasswordTemplateController extends Template<PERSON>ontroller
{
    function reset_password()
    {
        // save token before destroying session
        $csrf_token = \CSRFGuard::getCurrentToken();
        $timezone = $_SESSION['Timezone'];
        $fileVersion = $_SESSION['DATIX_FILE_VERSION'];
        $databaseVersion = $_SESSION['DATIX_DATABASE_VERSION'];
        $nonce = $_SESSION['nonce'];

        session_unset();
        @session_destroy();

        // now restore it
        session_start();
        GetParms();

        \CSRFGuard::setToken($csrf_token);
        $_SESSION['Timezone'] = $timezone;
        $_SESSION['DATIX_FILE_VERSION'] = $fileVersion;
        $_SESSION['DATIX_DATABASE_VERSION'] = $databaseVersion;
        $_SESSION['nonce'] = $_SESSION["Globals"]["NONCE"] = $nonce;

        // Get error and user values from query parameters
        $error = $this->request->getParameter('error');
        $user = $this->request->getParameter('user');
        $bothEmpty = $this->request->getParameter('both_empty') == '1';

        /*Determine which input fields should be marked as invalid based on the current error message.
         These boolean flags are passed to the view to conditionally apply error styling (e.g., red borders and error icons).
         Username input should display an error state for the following error messages:*/
        $usernameError = in_array($error, [
            _tk('user_name_error'),              // Username field is invalid or incorrect
            _tk('both_blank'),                   // Both Username and Captcha are blank
            _tk('invalid_username_and_captcha')  // Both fields are present but invalid
        ])|| $bothEmpty;

        // Captcha input should display an error state for the following error messages:
        $captchaError = in_array($error, [
            _tk('empty_captcha'),               // Captcha field is empty
            _tk('invalid_captcha'),             // Captcha is incorrect
            _tk('invalid_call'),                // Invalid captcha attempt
            _tk('both_blank'),                  // Both fields are blank
            _tk('invalid_username_and_captcha') // Both fields are invalid
        ])|| $bothEmpty;

        $this->title = _tk('reset_password_title');

        // Build and render the Reset Password view with necessary data
        $this->response->build('src/login/views/ResetPassword.php', array(
            'error' => $error,
            'user' => $user,
            'usernameError' => $usernameError ?? '',
            'captchaError' => $captchaError ?? ''
        ));

    }

    function reset_password2()
    {   
      $username = $this->request->getParameter('username');
      $captcha = $_POST['captcha'] ?? '';

    // CASE: Both username and captcha are empty
    // Show legacy error message and set flag to highlight both fields
    if ($username === '' && $captcha === '') {
        $this->redirect('index.php?action=reset_password&error=' . _tk('user_name_error') . '&both_empty=1');
    }

    // CASE: Captcha is missing (regardless of whether username is valid or invalid)
    // According to legacy flow, show captcha-specific error
    if ($captcha === '') {
        $this->redirect('index.php?action=reset_password&error=' . _tk('empty_captcha'));
    }

    // CASE: Captcha is present, but username is empty
    // Show username-specific error
    if ($username === '') {
        $this->redirect('index.php?action=reset_password&error=' . _tk('user_name_error'));
    }

    // CAPTCHA is filled → now check user validity
    $Factory = new UserModelFactory();
    $User = $Factory->getMapper()->findByLogin($username);

    $isInvalidUser = ($User->login_tries == -1 || $User->con_email == '');
    $isInvalidCaptcha = ($_SESSION['captcha'] !== $captcha);

    // CASE: Username is valid but captcha is invalid
    // Show captcha-specific error
    if ($isInvalidCaptcha && !$isInvalidUser) {
        unset($_SESSION['captcha']);
        $this->redirect('index.php?action=reset_password&error=' . _tk('invalid_captcha'));
    }

    // CASE: Both username and captcha are invalid
    // Legacy flow prioritizes captcha error
    if ($isInvalidCaptcha && $isInvalidUser) {
        unset($_SESSION['captcha']);
        $this->redirect('index.php?action=reset_password&error=' . _tk('invalid_captcha'));
    }

    // CASE: Username is invalid and captcha is valid
    // Handle based on enumeration prevention setting
    if ($isInvalidUser && !$isInvalidCaptcha) {
        if (bYN(GetParm('PREVENT_USERNAME_ENUMERATION', 'N'))) {
            success_message(_tk('pwd_reset_success'));
            return;
        } else {
            $this->redirect('index.php?action=reset_password&error=' . _tk('user_name_error'));
        }
    }

    // CASE: All inputs are valid
    // Proceed with password reset logic
    unset($_SESSION['captcha']);

    $passwordHasher = new PasswordHasher();
    $md5_change_password = $passwordHasher->hash(time());
    $User->sta_pwd_reset_code = $md5_change_password;

    $sql = 'UPDATE staff
            SET sta_pwd_reset_code = :sta_pwd_reset_code
            WHERE login = :login';
    \DatixDBQuery::PDO_query($sql, array(
        "login" => $username,
        "sta_pwd_reset_code" => $md5_change_password
    ));

    AuditLogin($username, "User requested password reset");

    $emailSender = EmailSenderFactory::createEmailSender('ADM', 'ResetPassword');
    $emailSender->addRecipient($User);
    $emailSender->sendEmails(['resetCode' => $md5_change_password]);
    $sentSuccessfully = !empty($emailSender->getSuccessful());

    if (bYN(GetParm('PREVENT_USERNAME_ENUMERATION', 'N'))) {
        success_message(_tk('pwd_reset_success'));
    } else if ($sentSuccessfully == 1) {
        success_message(_tk('pwd_reset_email_sent'));
    } else {
        success_message(_tk('email_problem_sending'));
    }
  }
} 