<?php

namespace src\login\controllers;

use src\email\EmailSenderFactory;
use src\framework\controller\TemplateController;
use src\security\PasswordHasher;
use src\users\model\UserModelFactory;

class ResetPasswordTemplateController extends TemplateController
{
    function reset_password()
    {
        // save token before destroying session
        $csrf_token = \CSRFGuard::getCurrentToken();
        $timezone = $_SESSION['Timezone'];
        $fileVersion = $_SESSION['DATIX_FILE_VERSION'];
        $databaseVersion = $_SESSION['DATIX_DATABASE_VERSION'];
        $nonce = $_SESSION['nonce'];

        session_unset();
        @session_destroy();

        // now restore it
        session_start();
        GetParms();

        \CSRFGuard::setToken($csrf_token);
        $_SESSION['Timezone'] = $timezone;
        $_SESSION['DATIX_FILE_VERSION'] = $fileVersion;
        $_SESSION['DATIX_DATABASE_VERSION'] = $databaseVersion;
        $_SESSION['nonce'] = $_SESSION["Globals"]["NONCE"] = $nonce;

        // Get error and user values from query parameters
        $error = $this->request->getParameter('error');
        $user = $this->request->getParameter('user');

        /*Determine which input fields should be marked as invalid based on the current error message.
         These boolean flags are passed to the view to conditionally apply error styling (e.g., red borders and error icons).
         Username input should display an error state for the following error messages:*/
        $usernameError = in_array($error, [
            _tk('user_name_error'),              // Username field is invalid or incorrect
            _tk('both_blank'),                   // Both Username and Captcha are blank
            _tk('invalid_username_and_captcha')  // Both fields are present but invalid
        ]);

        // Captcha input should display an error state for the following error messages:
        $captchaError = in_array($error, [
            _tk('empty_captcha'),               // Captcha field is empty
            _tk('invalid_captcha'),             // Captcha is incorrect
            _tk('invalid_call'),                // Invalid captcha attempt
            _tk('both_blank'),                  // Both fields are blank
            _tk('invalid_username_and_captcha') // Both fields are invalid
        ]);

        $this->title = _tk('reset_password_title');

        // Build and render the Reset Password view with necessary data
        $this->response->build('src/login/views/ResetPassword.php', array(
            'error' => $error,
            'user' => $user,
            'usernameError' => $usernameError ?? '',
            'captchaError' => $captchaError ?? ''
        ));

    }

    function reset_password2()
    {   
       $username = $this->request->getParameter('username');
        $captcha = $_POST['captcha'] ?? '';

        // First check: Both username and captcha are empty
        if ($username === '' && $captcha === '') {
            $this->redirect('index.php?action=reset_password&error=' . _tk('both_blank'));
        }

        // Check if username is present and valid
        $Factory = new UserModelFactory();
        $User = $Factory->getMapper()->findByLogin($username);
        $isInvalidUser = ($username === '' || $User->login_tries == -1 || $User->con_email == '');

        // Check if captcha is invalid
        $isInvalidCaptcha = ($captcha === '' || $_SESSION['captcha'] !== $captcha);

        // Handle case: Both fields filled but both invalid
        if ($isInvalidUser && $isInvalidCaptcha) {echo"invalidboth";
            unset($_SESSION['captcha']);
            $this->redirect('index.php?action=reset_password&error=' . _tk('invalid_username_and_captcha'));
        }

        // Handle username invalid only
        if ($isInvalidUser) {
            if (bYN(GetParm('PREVENT_USERNAME_ENUMERATION', 'N'))) {
                success_message(_tk('pwd_reset_success'));
                return;
            } else {
                $this->redirect('index.php?action=reset_password&error=' . _tk('user_name_error'));
            }
        }

        // Handle captcha invalid only
        if ($isInvalidCaptcha) {
            unset($_SESSION['captcha']);
            if ($captcha === '') {
                $this->redirect('index.php?action=reset_password&error=' . _tk('empty_captcha'));
            } else {
                $this->redirect('index.php?action=reset_password&error=' . _tk('invalid_captcha'));
            }
        }

        // If we reached here, both username and captcha are valid
        unset($_SESSION['captcha']);

        //We create a random string for the user to use to identify themselves when they return to set a new password
        $passwordHasher = new PasswordHasher();
        $md5_change_password = $passwordHasher->hash(time());

        $User->sta_pwd_reset_code = $md5_change_password;

        $sql = 'UPDATE staff
        SET sta_pwd_reset_code = :sta_pwd_reset_code
        WHERE login = :login';
        \DatixDBQuery::PDO_query($sql, array("login" => $username, "sta_pwd_reset_code" => $md5_change_password));

        AuditLogin($username, "User requested password reset");

        $emailSender = EmailSenderFactory::createEmailSender('ADM', 'ResetPassword');
        $emailSender->addRecipient($User);
        $emailSender->sendEmails(['resetCode' => $md5_change_password]);
        $sentSuccessfully = !empty($emailSender->getSuccessful());

        if (bYN(GetParm('PREVENT_USERNAME_ENUMERATION', 'N'))) {
            success_message(_tk('pwd_reset_success'));

        } else if ($sentSuccessfully == 1) {
            success_message(_tk('pwd_reset_email_sent'));

        } else {
            success_message(_tk('email_problem_sending'));
        }
    }
}