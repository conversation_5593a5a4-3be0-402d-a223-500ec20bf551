/* Login */
 
#login .login-wrap {
    /* padding:10px; margin:20px; background-color:#E8EDF1; border:3px solid #D1DCE2; */
    background-color: white; /*  Clean white background for the form */
    /* padding: 30px; Space inside the login box */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Soft shadow for depth */
    width: auto;
    display: flex;
    /* width: 418px; */
    /* padding: 16px 0px; */
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
    gap: 16px;
    border: 1px solid var(--Border-emphasis, rgba(0, 112, 106, 0.16));
    border: 1px solid var(--Border-emphasis, color(display-p3 0.0745 0.4235 0.4078 / 0.16));
    box-shadow: 0px 1px 3px 0px var(--global-color-alpha-black-200, rgba(0, 0, 0, 0.07));
    box-shadow: 0px 1px 3px 0px var(--global-color-alpha-black-200, color(display-p3 0 0 0 / 0.07));
  }
 
#login {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #EDF1F2;
    /*height: 100vh; Centers the box vertically */
}
.login-table-container{
    width: 418px;
}
 
#login .login-wrap td {border:none; padding: 8px;}
 
#login .login-wrap h1 {
    color:  #2F3939 !important;
    color: var(--Text-regular, color(display-p3 0.1843 0.2235 0.2235));
    font-feature-settings: 'ss01' on, 'ss03' on, 'ss08' on;
    font-family: Geist;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: -0.18px;
    height: 26px;
    flex: 1 0 0;
}
#login .login-wrap td label {
    color: var(--text-regular, #373737);
    color: var(--text-regular, color(display-p3 0.2157 0.2157 0.2157));
    font-family: Geist;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    width: 70px;
}
#login .login-wrap td input[type="text"],
#login .login-wrap td input[type="password"] {border:2px solid #D1DCE2; padding:5px; width:100%; }
 
#login .login-wrap td input[type="submit"] {
    font-size:16px;    
    height: var(--vertical-base, 32px);
    min-width: 70px;
    min-height: var(--component-height-regular, 28px);
    padding: var(--Next, 4px) var(--Near, 16px);
    align-items: center;}
 
#login #H {height:100%;}
#login #H .login-options {padding:10px; color:#fff;}
 
#login #H .login-options .time {font-size:12px; font-weight:bold; margin-bottom:5px;}
#login #H .login-options .menu a {color:#fff; font-size:14px; font-weight:bold;}
#login #H .login-options .menu a:hover {color:#FC0;}
 
 .table #login_form_table {
    display: flex;
    width: 418px;
    padding: 16px 0px;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
    gap: 16px;
    border-radius: 8px;
    border: 1px solid var(--Border-emphasis, rgba(0, 112, 106, 0.16));
    border: 1px solid var(--Border-emphasis, color(display-p3 0.0745 0.4235 0.4078 / 0.16));
    background: #FFF;
    background: color(display-p3 1 1 1);
    /* nova/shadow/gentle */
    box-shadow: 0px 1px 3px 0px var(--global-color-alpha-black-200, rgba(0, 0, 0, 0.07));
    box-shadow: 0px 1px 3px 0px var(--global-color-alpha-black-200, color(display-p3 0 0 0 / 0.07));
}
.login_form_inner_table{
    /* padding: 0 16px; */
    gap: 16;
}
 .login_form_control{
    display: flex;
    align-items: center;
    padding: 4px 16px;
    margin-bottom: 8px;
 }
 .form_control-dropdown{
    display: flex;
    align-items: center;
    padding: 4px 16px;
    margin-bottom: 16px;
 }
.login_label_text{
    color:  #373737 !important;
    color: var(--text-regular, color(display-p3 0.2157 0.2157 0.2157));
    font-family: Geist;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    min-width: 70px;
    margin-right: 8px;
}
 
.submit_button {
    display: flex;
    height: var(--vertical-base, 32px);
    min-width: 70px;
    min-height: var(--component-height-regular, 28px);
    padding: var(--Next, 4px) var(--Near, 16px);
    justify-content: center;
    align-items: center;
    gap: var(--Next, 4px);
    border-radius: var(--interaction-border-radius, 6px);
    background:#1F57BA !important;
    border: none;
    color: #FFFFFF !important;
    /*font*/
    text-align: center;
    font-feature-settings: 'ss01' on, 'ss03' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: 124%; /* 16.12px */
}
 
.login-container-center-only{
    display: flex;
    justify-content: center;
    padding-bottom: 16px;
}
 
.login-container-with-forgot{
   display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
}
 
.login_input_box, .login_select_box{
    display: flex;
    min-height: 28px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    min-width: 308px;
    border-radius: 6px;
    border: 1px solid var(--border-normal, #CDCDCD) !important;
    background:  #FFFFFF !important;
    color:#373737 !important;
    font-feature-settings: 'ss01' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 21px */
    letter-spacing: -0.14px;
    padding: 0px 8px 0px 8px; /* for arrow spacing */
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}
 
/* Wrapper for positioning the custom arrow */
.custom-select-wrapper {
  position: relative;
  display: inline-block;
}
 
/* Custom arrow icon inside select dropdown, spaced 8px from right */
.custom-select-wrapper::after {
  content: "";
  background: url(../../../Images/Expandmore.svg) no-repeat center center;
  /*background-size: 12px;  adjust arrow size */
  position: absolute;
  width: 20px;
  height: 20px;
  right: 8px; /* 8px space after arrow before the border */
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;  
  margin-left:10px;
}
 
/*
 * ================================
 * Login Error Message Styles
 * ================================
 */
/*
 * Wrapper for the entire login error section.Uses flex layout to align main error message and help icon. */
.login-error-wrapper {
    /* display: flex; */
    justify-content: center;
    align-items: center;
    flex: 1 0 0;
    gap: 8px; /* Creates space between the error message text and the help icon */
}
.titlebg{
    padding: 16px;
}
/*
 * Controls the spacing below the error wrapper.Applied when detailed error description is hidden. */
/* .login-error-margin-lg {
    margin-bottom: 16px;
} */
/*
 * Applied when detailed error description is shown. Reduces spacing between the main error and extra description.
 * This gives a grouped look between "Login attempt unsuccessful" and the "This may be due to..." message.*/
.login-error-margin-sm {
    margin-top: 8px;
}
/*
 * Adds a small top margin to visually separate this element from the content above it (e.g., error message box).
 * Used for login attempts message when displayed below the error.*/
.login-error-margin-top-sm{
    margin-top: 8px;
    display:block;
}
/*
 * Main error message styling box (e.g., "Login attempt unsuccessful").
   Applies background color, border, and font styling to visually indicate an error.*/
.login-error-box {
    padding: var(--nova-spacing-next, 4px) var(--nova-spacing-tight, 8px);
    width: 100%;
    display: block;
    border-radius: var(--nova-radii-component-user-feedback, 4px);
    border: 1px solid var(--nova-color-error-primary,#D52020) !important;
    background: var(--nova-color-error-secondary, #FFD6D6) !important;
    color: var(--nova-color-error-accent, #4F0808) !important;
    font-feature-settings: 'ss01' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: var(--global-typography-fontsize-14, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
}
/*
 * Help icon next to the error message.
 * Clicking toggles the visibility of detailed error explanation.
 */
.login-error-help-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
}
/*
 * Styling for additional error description text (e.g.,
 * "This may be due to an incorrect user name or password...").
 */
.login-error-extra-text {
    margin-top: 8px;
    color: var(--nova-color-error-primary, #D52020);
    font-feature-settings: 'ss01' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: var(--global-typography-fontsize-12, 12px);
    font-style: normal;
    font-weight: 400;
    line-height: 120%;
    font-size:14px;
}
/*
 * Forgot Password Link Styling
 * ----------------------------
 * This targets the <a> tag inside the .forgot-password-wrapper container.The !important declarations are used to override global <a> tag styles
 * from global.css (like color, font, underline).
 */
.forgot-password-wrapper a{
    color: #1F57BA !important;
    text-align: center;
    font-feature-settings: 'ss01' on, 'ss03' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: 124%; /* 16.12px */
}
 