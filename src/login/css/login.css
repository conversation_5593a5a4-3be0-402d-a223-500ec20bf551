/* Login */
 
#login .login-wrap {
    /* padding:10px; margin:20px; background-color:#E8EDF1; border:3px solid #D1DCE2; */
    background-color: white; /*  Clean white background for the form */
    /* padding: 30px; Space inside the login box */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Soft shadow for depth */
    width: auto;
    display: flex;
    /* width: 418px; */
    /* padding: 16px 0px; */
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
    gap: 16px;
    border: 1px solid var(--Border-emphasis, rgba(0, 112, 106, 0.16));
    border: 1px solid var(--Border-emphasis, color(display-p3 0.0745 0.4235 0.4078 / 0.16));
    box-shadow: 0px 1px 3px 0px var(--global-color-alpha-black-200, rgba(0, 0, 0, 0.07));
    box-shadow: 0px 1px 3px 0px var(--global-color-alpha-black-200, color(display-p3 0 0 0 / 0.07));
  }
 
#login {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #EDF1F2;
    /*height: 100vh; Centers the box vertically */
}
.login-table-container{
    width: 418px;
}
 
#login .login-wrap td {border:none; padding: 8px;}
 
#login .login-wrap h1 {
    color:  #2F3939 !important;
    color: var(--Text-regular, color(display-p3 0.1843 0.2235 0.2235));
    font-feature-settings: 'ss01' on, 'ss03' on, 'ss08' on;
    font-family: Geist;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: -0.18px;
    height: 26px;
    flex: 1 0 0;
}
#login .login-wrap td label {
    color: var(--text-regular, #373737);
    color: var(--text-regular, color(display-p3 0.2157 0.2157 0.2157));
    font-family: Geist;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    width: 70px;
}
#login .login-wrap td input[type="text"],
#login .login-wrap td input[type="password"] {border:2px solid #D1DCE2; padding:5px; width:100%; }
 
#login .login-wrap td input[type="submit"] {
    font-size:16px;    
    height: var(--vertical-base, 32px);
    min-width: 70px;
    min-height: var(--component-height-regular, 28px);
    padding: var(--Next, 4px) var(--Near, 16px);
    align-items: center;}
 
#login #H {height:100%;}
#login #H .login-options {padding:10px; color:#fff;}
 
#login #H .login-options .time {font-size:12px; font-weight:bold; margin-bottom:5px;}
#login #H .login-options .menu a {color:#fff; font-size:14px; font-weight:bold;}
#login #H .login-options .menu a:hover {color:#FC0;}
 
 .table #login_form_table {
    display: flex;
    width: 418px;
    padding: 16px 0px;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
    gap: 16px;
    border-radius: 8px;
    border: 1px solid var(--Border-emphasis, rgba(0, 112, 106, 0.16));
    border: 1px solid var(--Border-emphasis, color(display-p3 0.0745 0.4235 0.4078 / 0.16));
    background: #FFF;
    background: color(display-p3 1 1 1);
    /* nova/shadow/gentle */
    box-shadow: 0px 1px 3px 0px var(--global-color-alpha-black-200, rgba(0, 0, 0, 0.07));
    box-shadow: 0px 1px 3px 0px var(--global-color-alpha-black-200, color(display-p3 0 0 0 / 0.07));
}
.login_form_inner_table{
    /* padding: 0 16px; */
    gap: 16;
}
 .login_form_control{
    display: flex;
    align-items: center;
    padding: 4px 16px;
    margin-bottom: 8px;
 }
 .form_control-dropdown{
    display: flex;
    align-items: center;
    padding: 4px 16px;
    margin-bottom: 16px;
 }
.login_label_text{
    color:  #373737 !important;
    color: var(--text-regular, color(display-p3 0.2157 0.2157 0.2157));
    font-family: Geist;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    min-width: 70px;
    margin-right: 8px;
}

.login-container-center-only{
    display: flex;
    justify-content: center;
    padding-bottom: 16px;
}
 
.login-container-with-forgot{
   display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    padding-left: 16px;
    padding-right: 16px;
    margin-bottom: 16px;
    margin-top:16px;
}
 
/* Wrapper for positioning the custom arrow */
.custom-select-wrapper {
  position: relative;
  display: inline-block;
}
 
/* Custom arrow icon inside select dropdown, spaced 8px from right */
.custom-select-wrapper::after {
  content: "";
  background: url(../../../Images/Expandmore.svg) no-repeat center center;
  /*background-size: 12px;  adjust arrow size */
  position: absolute;
  width: 20px;
  height: 20px;
  right: 8px; /* 8px space after arrow before the border */
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;  
  margin-left:10px;
}
 
/*
 * ================================
 * Login Error Message Styles
 * ================================
 */
/*
 * Wrapper for the entire login error section.Uses flex layout to align main error message and help icon. */
.login-error-wrapper {
    /* display: flex; */
    justify-content: center;
    align-items: center;
    flex: 1 0 0;
    gap: 8px; /* Creates space between the error message text and the help icon */
}
.titlebg{
    padding: 16px;
}
/*
 * Controls the spacing below the error wrapper.Applied when detailed error description is hidden. */
/* .login-error-margin-lg {
    margin-bottom: 16px;
} */
/*
 * Applied when detailed error description is shown. Reduces spacing between the main error and extra description.
 * This gives a grouped look between "Login attempt unsuccessful" and the "This may be due to..." message.*/
.login-error-margin-sm {
    margin-top: 8px;
}
/*
 * Adds a small top margin to visually separate this element from the content above it (e.g., error message box).
 * Used for login attempts message when displayed below the error.*/
.login-error-margin-top-sm{
    margin-top: 8px;
    display:block;
}
/*
 * Main error message styling box (e.g., "Login attempt unsuccessful").
   Applies background color, border, and font styling to visually indicate an error.*/
.login-error-box {
    padding: var(--nova-spacing-next, 4px) var(--nova-spacing-tight, 8px);
    width: 100%;
    display: block;
    border-radius: var(--nova-radii-component-user-feedback, 4px);
    border: 1px solid var(--nova-color-error-primary,#D52020) !important;
    background: var(--nova-color-error-secondary, #FFD6D6) !important;
    color: var(--nova-color-error-accent, #4F0808) !important;
    font-feature-settings: 'ss01' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: var(--global-typography-fontsize-14, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
}
/*
 * Help icon next to the error message.
 * Clicking toggles the visibility of detailed error explanation.
 */
.login-error-help-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
}
/*
 * Styling for additional error description text (e.g.,
 * "This may be due to an incorrect user name or password...").
 */
.login-error-extra-text {
    margin-top: 8px;
    color: var(--nova-color-error-primary, #D52020);
    font-feature-settings: 'ss01' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: var(--global-typography-fontsize-12, 12px);
    font-style: normal;
    font-weight: 400;
    line-height: 120%;
    font-size:14px;
}
/*
 * Forgot Password Link Styling
 * ----------------------------
 * This targets the <a> tag inside the .forgot-password-wrapper container.The !important declarations are used to override global <a> tag styles
 * from global.css (like color, font, underline).
 */
.forgot-password-wrapper a{
    color: #1F57BA !important;
    text-align: center;
    font-feature-settings: 'ss01' on, 'ss03' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: 124%; /* 16.12px */
}
/* ============================= */
/* Login Textbox Interactive States */
/* ============================= */

/* Hover state: Changes border color on hover */
.login_input_box:hover {
    border: 1px solid var(--border-emphasis, #889192) !important;
}

.login_input_box:focus {
  /* border-radius: 6px; */

  background: var(--fds-surface-base, #FFFFFF);
  background: var(--fds-surface-base, color(display-p3 1 1 1));
  box-shadow: 0px 0px 0px 2px #0059C1 !important;
  box-shadow: 0px 0px 0px 2px color(display-p3 0.1211 0.3425 0.73);
  outline: none;
}
/* Active state: Applies a teal shadow and subtle border on active click */
.login_input_box:active { 
    border: 1px solid var(--border-emphasis, #889192) !important;
    box-shadow: 0px 0px 0px 2px #00D0BE !important;
    box-shadow: 0px 0px 0px 2px color(display-p3 0.2078 0.8 0.7451);
}

/* Errored state: Shows red border when validation fails */
.login_input_box.error {
    border: 1px solid #AF1212 !important;
}

/* -------------------------------
     Login Domain Dropdown and Inputbox – Base Styles
---------------------------------*/
/*  */
.login_input_box, .login_select_box {
  display: flex;
  min-height: 28px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  min-width: 308px;
  border-radius: 6px;
  border: 1px solid var(--border-normal, #CDCDCD) !important;
  background: #FFFFFF !important;
  color: #373737 !important;
  font-feature-settings: 'ss01' on, 'ss04' on, 'ss06' on, 'ss08' on;
  font-family: Geist;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: -0.14px;
  padding: 0px 8px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* Hover state: lighter border for visual feedback */
.login_select_box:hover {
  border: 1px solid var(--border-emphasis, #889192) !important;
  border: 1px solid var(--border-emphasis, color(display-p3 0.5333 0.5686 0.5725)) ;
  background: var(--fds-surface-base, #FFFFFF);
  background: var(--fds-surface-base, color(display-p3 1 1 1));
}

/* Focus state: blue glow ring on keyboard focus */
.login_select_box:focus {
  background: var(--fds-surface-base, #FFFFFF);
  background: var(--fds-surface-base, color(display-p3 1 1 1));
  box-shadow: 0px 0px 0px 2px #0059C1 !important;
  box-shadow: 0px 0px 0px 2px color(display-p3 0.1211 0.3425 0.73);
  outline: none;
}

/* Active state: teal highlight on click */
.login_select_box:active {
  border: 1px solid var(--border-emphasis, #889192) !important;
  border: 1px solid var(--border-emphasis, color(display-p3 0.5333 0.5686 0.5725));
  background: var(--fds-surface-base, #FFFFFF);
  background: var(--fds-surface-base, color(display-p3 1 1 1));
  box-shadow: 0px 0px 0px 2px #00D0BE !important;
  box-shadow: 0px 0px 0px 2px color(display-p3 0.2078 0.8 0.7451);
}

/* -------------------------------
   Login Button – Base Styles
---------------------------------*/
/* Base button styling using Nova tokens for layout, spacing, colors, and typography */
.submit_button {
  display: flex;
  height: 28px;
  padding: var(--nova-spacing-next, 4px) var(--nova-spacing-close, 12px);
  justify-content: center;
  align-items: center;
  gap: var(--nova-spacing-next, 4px);
  border-radius: var(--nova-radii-softened, 8px);
  background: var(--nova-button-primary-default-color-primary, #1F4CB3) !important;
  background: var(--nova-button-primary-default-color-primary, color(display-p3 0.1216 0.298 0.702));
  color: var(--nova-button-primary-default-color-neutral, #FFF);
  color: var(--nova-button-primary-default-color-neutral, color(display-p3 1 1 1));
  font-family: var(--nova-typography-font-stack, Geist);
  font-size: var(--global-typography-fontsize-14, 14px);
  font-style: normal;
  font-weight: 400;
  line-height: 14px;
  flex-shrink: 0;
  border:0;
}

/* ----------------------------------
   Hover State – Darker background on hover
----------------------------------- */
.submit_button:hover {
  background: var(--nova-button-primary-default-color-secondary, #1E5EDB) !important;
  background: var(--nova-button-primary-default-color-secondary, color(display-p3 0.1176 0.3686 0.8588));
}

/* ----------------------------------
   Focus State – Button is focused Same background + outline for accessibility
-----------------------------------*/
.submit_button:focus {
  top: -2px;
  left: -2px;
  border-radius: 10px;
  box-shadow: 0 0 0 2px #3773DB !important; /* Solid blue focus ring */
  outline: none;
}

/* ----------------------------------
   Active State – Button is pressed Darkest background + slight press effect
-----------------------------------*/
.submit_button:active {
  background: var(--nova-button-primary-default-color-tertiary, #1E428A) !important;
}

/* ===== Start: Dropdown Menu Styles ===== */
/* Displays the selected option value inside the dropdown button */
.selected-option {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background: #fff;
  font-size: 14px;
}

/* Dropdown container (ul) — hidden by default, positioned below the select box */
.login_select_box .custom-options {
  display: none;
  position: absolute;
  top: calc(100% + 2px);
  left: 0;
  width: 100%;
  height: 86px;
  max-height: 372px;
  overflow-y: auto;
  padding: var(--nova-spacing-tight, 8px);
  background: var(--nova-input-color-background, #FFFFFF) !important;
  z-index: 999;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--nova-spacing-next, 4px);
  flex-shrink: 0;
  border-radius: var(--nova-radii-softened, 8px);
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.13);
}

/* Makes the dropdown visible when the .open class is applied */
.login_select_box.open .custom-options {
  display: block;
}

/* Common styling for each <li> option in the dropdown list */
.login_select_box .custom-options li {
  display: flex;
  height: 32px;
  padding: 0px var(--nova-spacing-tight, 8px);
  align-items: center;
  gap: var(--nova-spacing-tight, 8px);
  align-self: stretch;
  transition: background 0.2s ease;
  color: var(--nova-link-option-color-neutral, #151D1E) !important; /* Non-selected menu font color */
  font-family: Geist;
  font-size: var(--global-typography-fontsize-14, 14px);
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
}

/* Hover state for non-selected options (mouse interaction) */
.login_select_box .custom-options li:hover {
  border-radius: var(--nova-spacing-next, 4px);
  background: var(--nova-link-option-color-accent, #E9F7F5) !important;
}

/* Focus state for non-selected options (keyboard tab/focus) */
.login_select_box .custom-options li:focus {
  outline: 2px solid #3773DB;
  background-color: #E9F7F5;
  border-radius: var(--nova-spacing-next, 4px);
}

/* Active (mouse click) state for non-selected options */
.login_select_box .custom-options li:active {
  border-radius: 4px;
  background: var(--table-row-selected-odd, #BFE0FE);
  color: var(--nova-link-option-color-neutral, #101D1E);
}

/* JS-applied focus class used during keyboard navigation */
.login_select_box .custom-options li.focus {
  border-radius: var(--nova-spacing-next, 4px);
  background: var(--nova-link-option-color-accent, #E9F7F5);
}

/* Styling for the selected option in the dropdown */
.login_select_box .custom-options li.selected {
  border-radius: var(--nova-spacing-next, 4px);
  background: var(--nova-link-option-color-primary, #14716D);
  color: #FFFFFF; /* Selected menu font color */
}

/* Hover styling for the selected item */
.login_select_box .custom-options li.selected:hover {
  background: var(--nova-link-option-color-primary, #148D87) !important;
}

/* Focus styling for the selected item (keyboard focus) */
.login_select_box .custom-options li.selected:focus {
  border-radius: var(--nova-spacing-next, 4px);
  border: none;
  box-shadow: 0 0 0 2px var(--button-focus, #9EF1E4) !important;
  box-shadow: 0 0 0 2px color(display-p3 0.6196 0.9451 0.8941);
  background: var(--nova-link-option-color-secondary, #009389);
  background: var(--nova-link-option-color-secondary, color(display-p3 0.0784 0.5529 0.5294));
}

/* ===== End: Dropdown Menu Styles ===== */