<?php
namespace src\generic\controllers;

use src\admin\model\systemFlags\RecordUpdateEmailDatetimeFlag;
use src\admin\model\SystemState;
use src\framework\controller\TemplateController;
use src\framework\registry\Registry;
use src\lfpse\controllers\LfpseServiceController;

class HomeScreens extends TemplateController
{
    /**
     * @desc Main homescreen function - accessed by action=home. Displays the appropriate
     * page depending on the module we are in.
     */
    function home()
    {
        global $ModuleDefs, $dtxtitle, $scripturl, $MinifierDisabled;
        
        $this->hasMenu = false;
        $this->hasPadding = false;

        $addMinExtension = ($MinifierDisabled ? '' : '.min');

        //Pick up module requested by the user.
        $UserModule = FirstNonNull(array(\Sanitize::getModule($_GET['module']), GetParm('LOGIN_DEFAULT_MODULE')));

        $ModuleToDisplay = GetDefaultModule(array('default' => $UserModule, 'needs_homescreen' => true));
        UnsetLFPSE();

        if (isset($_REQUEST['nhslist']) &&  $_REQUEST['nhslist'] == 'NHS' && $ModuleToDisplay == 'INC' && (IsFullAdmin() || CanManageLfpsePage()))
        {

            $lfpseServiceController = new LfpseServiceController();
            if(isset($_POST['nhskey']) && $_REQUEST['nhskey'] != '') {
                $lfpseServiceController->setSubscriptionKey(\Sanitize::SanitizeString($_REQUEST['nhskey']));
            }

            $this->response->build('src\lfpse\views\LfpseAudit.php', array(
                'subscriptionKey' => $lfpseServiceController->getSubscriptionKey(),
                'listNhsRecords' => $lfpseServiceController->listNhsResponse(),
                'checkHealth' => $lfpseServiceController->checkHealth(),
                'unSubmitedRecords' => $lfpseServiceController->getListOfResubmittedRecords(),
            ));

            return;
        }

        if($ModuleToDisplay == 'DAS')
        {
            $this->redirect($scripturl.'?action=dashboard');
        }

        if ($ModuleToDisplay == 'TOD')
        {
            $this->redirect($scripturl.'?action=list&module=TOD');
        }




        /*
        I think this should go further in the future - this script should be able to handle both Modules and Module Groups, but for now
        this is the least disruptive way of getting this in at this point in time. Refactoring ticket DW-6608 added to cover this in the future.
        */
        if($ModuleToDisplay == 'ACR')
        {
            $ModuleToDisplay = 'AMO';
        }
        if($ModuleToDisplay == 'CQC')
        {
            $ModuleToDisplay = 'CQO';
        }

        $dtxtitle = $ModuleDefs[$ModuleToDisplay]['MENU_NAME'] ?: $ModuleDefs[$ModuleToDisplay]['NAME'];

        $this->title = $dtxtitle;
        $this->module = $ModuleToDisplay;

        ob_start();

        if($ModuleToDisplay === 'ADM')
        {
            echo $this->GetAdminPage();
        }
        else if($ModuleToDisplay === 'AMO') {
            echo '<div class="parent-container">';
            echo $this->getAccreditationItems($ModuleToDisplay);
            echo $this->getAdditionalAccreditationItems($ModuleToDisplay);
            echo '</div>';
        }
        else
        {
            $Perms = GetParm($ModuleDefs[$ModuleToDisplay]['PERM_GLOBAL']);

            $MenuItems = GetMenuItems(array('module' => $ModuleToDisplay));

            $StatusList = ($ModuleDefs[$ModuleToDisplay]['HOME_SCREEN_STATUS_LIST']  && (!$ModuleDefs[$ModuleToDisplay]['INPUT_ONLY_LEVELS'] || !in_array($Perms, $ModuleDefs[$ModuleToDisplay]['INPUT_ONLY_LEVELS']))); //we need a status list on the right hand side of the screen.
            $pinnedGlobal = $this->registry->getParm($ModuleToDisplay.'_SAVED_QUERIES');

            $hasQueries = (bYN($this->registry->getParm($ModuleToDisplay.'_SAVED_QUERIES_HOME_SCREEN', 'N')) && ! empty($pinnedGlobal)) ? true : false;
            $showStatusList = (!$ModuleDefs[$ModuleToDisplay]['HIDE_STATUS_LIST']);

            echo '<div id="landing-page" class="row landing-page-content">
                        <div class="col '.((($showStatusList && $StatusList) || $hasQueries) ? 'gu2' : 'gu3').'">
                                <div class="content sidebar">

                                 <h1>'._tk('options').'</h1>
                                    <ul class="icon-bg">';

            foreach($MenuItems as $details)
            {
                if (isset($details['menu_name']))
                {
                    if (displaySectionHeading($details['items']))
                    {
                        echo '<li><div class="links background_pos' . $details['sprite_id'] . '">' . $details['menu_name'] . '</div><ul style="padding-left: 10px;">';

                        foreach ($details['items'] as $Items)
                        {
                            if($Items['condition'] !== false)
                            {
                                echo '<li><div class="nested-links background_pos'.$Items['sprite_id'].'">';

                                if ($Items['onclick'])
                                {
                                    echo '<a href="#" class="basic_link_csr_js" jsCall="'.$Items['onclick'].'">';
                                }
                                else
                                {
                                    echo '<a href="'.($Items['external_link'] ? '' : $scripturl.'?').$Items['link'].'"'.($Items['new_window'] ? ' target="_blank"' : '').'>';
                                }

                                echo ($Items['label']).'</a></div></li>';
                            }
                        }

                        echo '</ul></li>';
                    }
                }
                else
                {
                    if($details['condition'] !== false)
                    {
                        
                        if ($details['onclick'])
                        {
                            echo '<a href="#" class="basic_link_csr_js left_menu_a" jsCall="'.$details['onclick'].'">';
                            echo '<li><img src="Images/icons/'.$details['icon_svg'].'" alt="icon"/>';
                            echo _t($details['label']).'</li></a>';
                        }
                        else
                        {
                            echo '<a class="left_menu_a"';
                            if ($details['id']) {
                                echo 'id=' . $details['id'] . ' ';
                            }
                            echo 'href="'.($details['external_link'] ? '' : $scripturl.'?').$details['link'].'"'.($details['new_window'] ? ' target="_blank"' : '').'>';
                            echo '<li><img src="Images/icons/'.$details['icon_svg'].'" alt="icon"/> ';
                            echo _t($details['label']).'</li></a>';
                        }
                        
                    }
                }
            }

            echo '
                                    </ul>
                                   </div><!--content-->
                                <div class="clearfix"></div>
                        </div><!--col-->';

            
            if ($StatusList)
            {
                $showStatusList = (!$ModuleDefs[$ModuleToDisplay]['HIDE_STATUS_LIST']);

                if ($ModuleDefs[$ModuleToDisplay]['APPROVAL_LEVELS'])
                {
                    require_once 'Source/security/SecurityBase.php';
                    $secGroups = GetUserSecurityGroups($_SESSION['contact_login_id']);
                    if (bYN(GetParm('USE_ADVANCED_ACCESS_LEVELS','N')) && getCount($secGroups) > 0)
                    {
                        $unorderedApprovalStatuses = array();

                        $sql = 'SELECT
                            perm_value as access_level
                            from sec_groups
                            left join sec_group_permissions on sec_group_permissions.grp_id = sec_groups.recordid and item_code = :item_code
                            WHERE sec_groups.recordid IN ('.implode(' ,', $secGroups).')';

                        $accessLevels = \DatixDBQuery::PDO_fetch_all($sql, array('item_code' => $ModuleDefs[$ModuleToDisplay]['PERM_GLOBAL']), \PDO::FETCH_COLUMN);

                        foreach($accessLevels as $accessLevel)
                        {
                            $unorderedApprovalStatuses = array_merge($unorderedApprovalStatuses, GetLevelstoView($ModuleToDisplay, $accessLevel));
                        }

                        $sql = "
                            SELECT code_approval_status.code as code
                            FROM code_approval_status
                            JOIN link_access_approvalstatus
                                ON
                                  code_approval_status.code = link_access_approvalstatus.code
                                AND
                                  code_approval_status.module = link_access_approvalstatus.module
                                AND
                                  code_approval_status.workflow = link_access_approvalstatus.las_workflow
                            WHERE
                                  code_approval_status.module like '$ModuleToDisplay'
                                AND
                                  code_approval_status.workflow = ".GetWorkflowID($ModuleToDisplay)."
                            ORDER BY code_approval_status.cod_listorder";

                        $orderedApprovalStatuses = \DatixDBQuery::PDO_fetch_all($sql,array(), \PDO::FETCH_COLUMN);

                        foreach ($orderedApprovalStatuses as $orderedApprovalStatusCode)
                        {
                            if (isset($unorderedApprovalStatuses[$orderedApprovalStatusCode]))
                            {
                                $ApprovalStatuses[$orderedApprovalStatusCode] = $unorderedApprovalStatuses[$orderedApprovalStatusCode];
                            }
                        }
                    }
                    else
                    {
                        $ApprovalStatuses = GetLevelstoView($ModuleToDisplay, GetAccessLevel($ModuleToDisplay));
                    }

                    if (is_array($ApprovalStatuses))
                    {
                        unset($ApprovalStatuses['NEW']); //only want listable statuses.

                        $OverdueStatuses = array_keys(getOverdueStatuses(array('module' => $ModuleToDisplay)));

                        foreach($ApprovalStatuses as $Status => $properties)
                        {
                            $NumRecords = CountRecordsGeneric($ModuleToDisplay, "(rep_approved like '$Status')");

                            $ApprovalStatuses[$Status] = array(
                                'Label' => $properties['description'],
                                'Link' => $scripturl.'?action=list&module='.$ModuleToDisplay.'&listref='.$Status,
                                'RecordCount' => $NumRecords,
                                'Colour' => $properties['colour']
                            );

                            if (in_array($Status, $OverdueStatuses))
                            {
                                if(bYN(GetParm($ModuleToDisplay.'_HIDE_OVERDUE_NUMBERS')))
                                {
                                    $Details['NoOverdue'] = CheckRecordsExistGeneric($ModuleToDisplay, "(rep_approved like '$Status')", true, $Status) ? false : true;
                                }
                                else
                                {
                                    $NumRecordsOverdue = CountRecordsGeneric($ModuleToDisplay, "(rep_approved like '$Status')", true, $Status);
                                }

                                $ApprovalStatuses[$Status]['OverdueLink'] = $scripturl.'?action=list&module='.$ModuleToDisplay.'&listref='.$Status.'&overdue=1';
                                $ApprovalStatuses[$Status]['OverdueRecordCount'] = $NumRecordsOverdue;
                            }

                        }
                    }
                }
                else if (is_array($ModuleDefs[$ModuleToDisplay]['HARD_CODED_LISTINGS']))
                {
                    foreach ($ModuleDefs[$ModuleToDisplay]['HARD_CODED_LISTINGS'] as $Listing => $Details)
                    {
                        if((isset($ModuleDefs[$ModuleToDisplay]['WORKFLOW_GLOBAL']) && isset($Details['workflows']) && in_array(GetParm($ModuleDefs[$ModuleToDisplay]['WORKFLOW_GLOBAL'], $ModuleDefs[$ModuleToDisplay]['DEFAULT_WORKFLOW']), $Details['workflows']))
                        || (!isset($ModuleDefs[$ModuleToDisplay]['WORKFLOW_GLOBAL']) || !isset($Details['workflows'])))
                        {
                            $NumRecords = CountRecordsGeneric($ModuleToDisplay, $Details['Where']);
                            $NumRecordsOverdue = 0;
                            if (!empty($Details['OverdueWhere']))
                            {
                                if(bYN(GetParm($ModuleToDisplay.'_HIDE_OVERDUE_NUMBERS')))
                                {
                                    $Details['NoOverdue'] = CheckRecordsExistGeneric($ModuleToDisplay, TranslateWhereCom($Details['OverdueWhere'])) ? false : true;
                                }
                                else
                                {
                                    $NumRecordsOverdue = CountRecordsGeneric($ModuleToDisplay, TranslateWhereCom($Details['OverdueWhere']));
                                }
                            }

                            $ApprovalStatuses[$Listing] = array(
                                'Label' => $Details['Link'],
                                'Link' => $scripturl . '?action=list&amp;module='.$ModuleToDisplay.'&amp;listtype='.$Listing,
                                'RecordCount' => $NumRecords,
                                'OverdueRecordCount' => $NumRecordsOverdue,
                                'Colour' => $Details['Colour']
                            );

                            if(!$Details['NoOverdue'])
                            {
                                $ApprovalStatuses[$Listing]['OverdueLink'] = $scripturl . '?action=list&amp;module='.$ModuleToDisplay.'&amp;listtype='.$Listing.'&overdue=1';
                            }
                        }
                    }
                }

                $pinnedGlobal = $this->registry->getParm($ModuleToDisplay.'_SAVED_QUERIES');

                $hasQueries = (bYN($this->registry->getParm($ModuleToDisplay.'_SAVED_QUERIES_HOME_SCREEN', 'N')) && ! empty($pinnedGlobal)) ? true : false;

                ?>
                <?php if($hasQueries || (is_array($ApprovalStatuses) && $showStatusList)) { ?>
                <div class="col gu2-2 last">
                <div class="content right_section_landing_page">
                    <h1><?php echo _tk('searches'); ?></h1>
                    <div class="search_title_seperator"></div>
                    <ul class="icon-bg">
                        <?php if(is_array($ApprovalStatuses) && $showStatusList) :

                            if($hasQueries || $StatusList ): ?><li><div class="status-wrapper status_title"><?php echo _tk('statuses'); ?></div></li><?php endif;

                            $first = true;

                            foreach ($ApprovalStatuses as $Status => $Details) :

                                $has_overdue = (!empty($Details['OverdueLink']));

                                ?><li>
                                    <div class="status-card<?php echo ($first ? ' first' : ''); ?> <?php echo ($has_overdue ? 'has-overdue' : 'no-overdue'); ?>">
                                        <div class="status-main">
                                            <div class="status-left">
                                                <div class="status-info<?php echo ($has_overdue ? '' : ' no-overdue'); ?>">
                                                    <a href="<?php echo $Details['Link']; ?>">
                                                        <span class="status-dot" <?php echo ($Details['Colour'] ? 'style="background-color:#'.$Details['Colour'].'"' : ''); ?>></span>
                                                        <span class="status-text"><?php echo _t($Details['Label']); ?></span>
                                                    </a>
                                                </div>
                                            </div>

                                            <div class="status-records">
                                                <a href="<?php echo $Details['Link']; ?>">
                                                    <span class="record-count"><?php echo $Details['RecordCount'].' '._tk('records'); ?></span>
                                                    <span class="arrow"><img src="images/icons/icon_FORWARD_GRAY.svg" alt=""/></span>
                                                </a>
                                            </div>
                                        </div>

                                        <?php if($has_overdue): ?>
                                        <div class="status-overdue-section">
                                            <a href="<?php echo $Details['OverdueLink']; ?>">
                                                <span class="overdue-count">
                                                    <?php echo (bYN(GetParm($ModuleToDisplay.'_HIDE_OVERDUE_NUMBERS')) ? "" : $Details['OverdueRecordCount'] . ' ') ._tk('overdue'); ?>
                                                </span>
                                                <span class="arrow"><img src="images/icons/icon_FORWARD_RED.svg" alt=""/></span>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </li><?php

                                $first = false;

                            endforeach;

                        endif;?>
                    </ul>
                    <?php

                    /*
                     * PINNED SAVED QUERIES
                     */
                    if ($hasQueries)
                    {
                        $pinnedQueries = [];

                        foreach(explode(' ', $pinnedGlobal) as $pq)
                        {
                            $pinnedQueries[] = (int)$pq;
                        }

                        /* Limit down the query ID's by queries that actually exist. Because a user can effectively
                           put any numbers in here that they want, we need to validate against the database */
                        $pinnedQuerySQL = "SELECT recordid from queries
                                            WHERE sq_module = '".$ModuleToDisplay."'
                                            AND recordid IN(".implode(', ', $pinnedQueries).")";

                        $pinnedQueries = \DatixDBQuery::PDO_fetch_all($pinnedQuerySQL, [], \PDO::FETCH_COLUMN);

                        // We need to go over the $pinnedQueries array to order it the same has configurations panels are
                        $pinnedQueriesOrdered = [];

                        // If this causes a massive performance issue we need to call natsort before saving the values to the global
                        foreach ($pinnedQueries as $query_id)
                        {
                            $SavedQuery = (new \src\savedqueries\model\SavedQueryModelFactory())->getMapper()->findByOldQueryID($query_id);

                            $pinnedQueriesOrdered[$query_id] = htmlspecialchars($SavedQuery->name, ENT_COMPAT, 'UTF-8', false);
                        }

                        natsort($pinnedQueriesOrdered);

                        if (!empty($pinnedQueries))
                        {
                            ?><ul class="icon-bg">
                            <li><div class="pined_query_title"><?php echo _tk('pinned_queries'); ?></div></li>
                            <?php foreach ($pinnedQueriesOrdered as $query_id => $queryName) :

                            $SavedQuery = (new \src\savedqueries\model\SavedQueryModelFactory())->getMapper()->findByOldQueryID($query_id);
                            $SavedQueryHasAtPrompt = $SavedQuery->hasAtPrompt();

                            if (bYN(GetParm($ModuleToDisplay.'_SAVED_QUERIES_NUM_RECORDS', 'N')) && ($SavedQueryHasAtPrompt === false || $SavedQueryHasAtPrompt === null))
                            {
                                $query = $SavedQuery->createQuery();
                                $query->select([['COUNT(?) AS num', '*']]);

                                $writer = new \src\framework\query\QueryFactory();
                                $writer = $writer->getSqlWriter();
                                list($sql, $parameters) = $writer->writeStatement($query);

                                $result = \DatixDBQuery::PDO_fetch($sql, $parameters);
                                $numRecords = $result['num'];
                            }

                            $hasRecords = (bYN(GetParm($ModuleToDisplay.'_SAVED_QUERIES_NUM_RECORDS', 'N')) && ($SavedQueryHasAtPrompt === false || $SavedQueryHasAtPrompt === null)) ? true : false;
                            ?>
                            <li>
                                <div class="status-card<?php echo ($first ? ' first' : ''); ?> <?php echo ($has_overdue ? 'has-overdue' : 'no-overdue'); ?>">
                                    <div class="status-main">
                                        <div class="status-left">
                                            <div class="status-info name<?php echo ( ! $hasRecords)? ' no-records' : ''; ?>">
                                                <a href="<?php echo "$scripturl?action=executequery&module=$ModuleToDisplay&qry_recordid=".$query_id."&fromhomescreen=1"; ?>">
                                                    <img class="search_image_icon" src="images/icons/icon_SEARCH.svg" alt=""/>
                                                    <span class="status-text"><?php echo $queryName; ?></span>
                                                </a>
                                            </div>
                                        </div>
                                        <?php if ($hasRecords) : ?>
                                        <div class="status-records">
                                            <a href="<?php echo "$scripturl?action=executequery&module=$ModuleToDisplay&qry_recordid=".$query_id."&fromhomescreen=1"; ?>">
                                                <span class="record-count"><?php echo (isset($numRecords) ? $numRecords : '0') .' '._tk('records'); ?></span>
                                                <span class="arrow"><img src="images/icons/icon_FORWARD_GRAY.svg" alt=""/></span>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                            </li>
                        <?php endforeach; ?>
                            </ul><?php
                        }
                    }
                    ?></div>
                    <?php
                    }
                    ?></div>
                </div><?php
            } else {
                echo '
                <div class="col gu2-2 last">
                    <div class="content right_section_landing_page">
                        <h1>' . _tk('searches') . '</h1>
                        <div class="search_title_seperator"></div>
                        <div class="status-wrapper status_title">' . _tk('statuses') . '</div>
                        <p class="no-searches">No saved searches yet</p>
                        <div class="pined_query_title">' . _tk('pinned_queries') . '</div>
                        <p class="no-searches">No pinned queries yet</p>
                    </div>
                </div>';
            }
        }

        $output = ob_get_clean();

        $this->response->build('src\generic\views\HomeScreens.php', array(
            'output' => $output,
        ));
    }

    function GetAdminPage()
    {
        global $ModuleDefs, $scripturl;

        $recordUpdateEmailFlag = new RecordUpdateEmailDatetimeFlag();
        if ($recordUpdateEmailFlag->getState()->isBad())
        {
            AddSessionMessage('ERROR', _tk('record_update_email_error'));
        }

        $isGlobalsAdminHidden = IsCentrallyAdminSys() && !IsCentralAdmin() && bYN(GetParm('GLOBALS_ADMIN_LOCKED', 'N'));

        $KO41Title = _tk('ko41_export_title');
        $KO41Action = 'index.php?action=httprequest&type=ko41export';

        $KO41Link = "PopupDivFromURL(
                    'ko41export',
                    '" . $KO41Title . "',
                    '" . $KO41Action . "',
                    '',
                    [
                        {'value':'"._tk('btn_export')."','class':'ko41_export'},
                        {'value':'"._tk('btn_cancel')."','class':'ko41_cancel'}
                    ],
                    '240px',
                    '',
                    '');";

        $AdminLinkArray = array(
            'profile' => array(
                'label' => _t('Profile Settings'),
                'contents' => array(
                    array('label' => _tk('add_new_user'), 'link' => 'action=edituser', 'icon_svg'=>'icon_PLUS.svg', 'sprite_id' => 1, 'condition' => IsSubAdmin()),
                    array('label' => _tk('list_users'), 'link' => 'action=listusers', 'icon_svg'=>'icon_LIST.svg', 'sprite_id' => 2, 'condition' => IsSubAdmin()),
                    array('label' => _tk('manage_profiles'), 'link' => 'action=listprofiles', 'icon_svg'=>'icon_ATTACH.svg','sprite_id' => 3, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('list_groups'), 'link' => 'action=listgroups', 'icon_svg'=>'icon_USER_GROUP.svg', 'sprite_id' => 4, 'condition' => IsSubAdmin() || bYN(GetParm('ADM_GROUP_SETUP', 'N'))),
                    array('label' => _tk('change_pwd'), 'link' => 'action=password', 'icon_svg'=>'icon_STAR.svg', 'sprite_id' => 5, 'condition' => !$_SESSION['CurrentUser']->authenticated_via_sso && $_SESSION['CurrentUser']->con_sid == ''),
                    array('label' => _tk('approve_reg_reqs'), 'link' => 'action=listawaitingapproval', 'icon_svg'=>'icon_CHECKED.svg', 'sprite_id' => 18, 'condition' => (IsSubAdmin() && bYN(GetParm('DIF_2_REGISTER', 'N')))),
                    array('label' => _tk('view_own_user'), 'link' => 'action=edituser&recordid='.$_SESSION['contact_login_id'], 'icon_svg'=>'icon_USER_GROUP.svg', 'sprite_id' => 4, 'condition' => (bYN(GetParm('ADM_VIEW_OWN_USER', 'N'))))
                ),
            ),
            'system' => array(
                'label' => _t('System Settings'),
                'contents' => array(
                    array('label' => _tk('datixweb_config'), 'link' => 'action=setup', 'icon_svg'=>'icon_SETTING.svg', 'sprite_id' => 6, 'condition' => isGeneralAdmin() || isKO41Admin()),
                    array('label' => _tk('show_config_settings'), 'link' => 'action=showconfig', 'icon_svg'=>'icon_SETTING.svg', 'sprite_id' => 7, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('show_error_log'), 'link' => 'action=showerrorlog', 'icon_svg'=>'icon_ERROR.svg', 'sprite_id' => 8, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('show_locked_records'), 'link' => 'action=lockedrecords', 'icon_svg'=>'icon_LOCK.svg',  'sprite_id' => 9, 'condition' => bYN(GetParm("RECORD_LOCKING","N"))),
                    array('label' => _tk('show_user_sessions'), 'link' => 'action=usersessions', 'icon_svg'=>'icon_USER_ACCOUNT.svg', 'sprite_id' => 10, 'condition' => (IsSubAdmin() && bYN(GetParm('RECORD_LOCKING', 'N')))),
                    array('label' => _tk('import_xml_data'), 'link' => 'action=import_form', 'icon_svg'=>'icon_DMD_import.svg', 'sprite_id' => 11, 'condition' => (IsFullAdmin() && bYN(GetParm('WEB_XML_IMPORT', 'Y',true)))),
                    array('label' => _tk('import_table_data'), 'link' => 'action=importtabledata', 'icon_svg'=>'icon_IN_WORKING.svg', 'sprite_id' => 33, 'condition' => IsFullAdmin() && bYN($this->registry->getParm('DATIX_SUPPORT_ACCOUNT', 'N'))),
                    array('label' => _t('List extra fields'), 'link' => 'action=listextrafields', 'icon_svg'=>'icon_LIST.svg', 'sprite_id' => 2, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('globals'), 'link' => 'action=listglobals', 'icon_svg'=>'icon_SETTING.svg', 'sprite_id' => 6, 'condition' => (IsFullAdmin() && !$isGlobalsAdminHidden)),
                    array('label' => _tk('public-holidays'), 'link' => 'action=publicholidays', 'icon_svg'=>'icon_HOLIDAY.svg', 'sprite_id' => 38,  'condition' => isGeneralAdmin()),
                    array('label' => _tk('workflow-administration'), 'link' => 'action=workflowadmin', 'icon_svg'=>'icon_WORKFLOW.svg', 'sprite_id' => 42,  'condition' => isGeneralAdmin() && (ModIsLicensed('INC') || ModIsLicensed('RAM') || ModIsLicensed('PAL') || ModIsLicensed('COM') || ModIsLicensed('CON'))),
                    array('label' => _tk('ldap_connections'), 'link' => 'action=listldap', 'icon_svg'=>'icon_LDAP.svg', 'sprite_id' => 41,  'condition' => isGeneralAdmin()),
                    array(
                        'label' => _t('System Security'),
                        'contents' => array(
                            array('label' => _tk('login_settings-title'), 'link' => 'action=login_settings', 'icon_svg'=>'icon_SETTING.svg', 'sprite_id' => 37, 'condition' => isGeneralAdmin()),
                            array('label' => _tk('locked-users-title'), 'link' => 'action=locked_users', 'icon_svg'=>'icon_LOCKED_OUT_USER.svg', 'sprite_id' => 36, 'condition' => isGeneralAdmin()),
                            array('label' => _tk('full_audit_title'), 'link' => 'action=full_audit', 'icon_svg'=>'icon_BOOK.svg', 'sprite_id' => 21, 'condition' => isGeneralAdmin()),
                            array('label' => _tk('login_audit'), 'link' => 'action=show_login_audit', 'icon_svg'=>'icon_BOOK.svg', 'sprite_id' => 21, 'condition' => isGeneralAdmin()),
                            array('label' => _tk('email_audit_title'), 'link' => 'action=email_audit', 'icon_svg'=>'icon_BOOK.svg', 'sprite_id' => 21, 'condition' => isGeneralAdmin()),
                            array('label' => _tk('password-policy'), 'link' => 'action=password_policy', 'icon_svg'=>'icon_SETTING.svg', 'sprite_id' => 6, 'condition' => isGeneralAdmin()),
                            array('label' => _tk('dashboard_ownership'), 'link' => 'action=dashboard_ownership', 'icon_svg'=>'icon_SETTING.svg', 'sprite_id' => 100, 'condition' => isGeneralAdmin()),
                            array('label' => _tk('manage_saved_queries'), 'link' => 'action=manage_saved_queries', 'icon_svg'=>'icon_SETTING.svg', 'sprite_id' => 101, 'condition' => isGeneralAdmin())
                        ),
                        'condition' => true
                    ),
                ),
            ),
            'custom' => array(
                'label' => _t('Custom Settings'),
                'contents' => array(
                    array('label' => _tk('reports_administration'), 'link' => 'action=reportsadminlist', 'icon_svg'=>'icon_REPORT_BAR.svg', 'sprite_id' => 12, 'condition' => bYN(GetParm('ADM_NO_ADMIN_REPORTS'), 'N') || isGeneralAdmin()),
                    array('label' => _tk('document_template_administration'), 'link' => 'action=doctemplateadminlist','icon_svg'=>'icon_DOCUMENT.svg', 'sprite_id' => 13, 'condition' => (isGeneralAdmin() && bYN(GetParm('WORD_MERGE_WEB', 'N')) && (ModIsLicensed('INC') || ModIsLicensed('RAM') || ModIsLicensed('PAL') || ModIsLicensed('COM') || ModIsLicensed('CLA') || ModIsLicensed('STN') || ModIsLicensed('SAB')))),
                    array('label' => _tk('design_forms'), 'link' => 'action=listformdesigns', 'icon_svg'=>'icon_DESIGN_FORM.svg', 'sprite_id' => 14, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('design_listing_pages'), 'link' => 'action=listlistingdesigns', 'icon_svg'=>'icon_DESIGN_FORM.svg', 'sprite_id' => 15, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('manage_email_templates'), 'link' => 'action=listemailtemplates', 'icon_svg'=>'icon_EMAIL.svg', 'sprite_id' => 16, 'condition' => (isGeneralAdmin() && bYN(GetParm('EMAIL_TEMPLATES', 'N')))),
                    array('label' => _tk('overdue_email'), 'link' => 'action=overdueemails', 'icon_svg'=>'icon_TIME.svg', 'sprite_id' => 17, 'condition' => (isGeneralAdmin() && ModIsLicensed('INC'))),
                    array('label' => _tk('code_setups'), 'link' => 'action=codesetupslist', 'icon_svg'=>'icon_SETTING.svg', 'sprite_id' => 6, 'condition' => (isGeneralAdmin() || $this->CheckSetupAllowed() || isKO41Admin())),
                    array('label' => _tk('combo_linking'), 'link' => 'action=listcombolinks', 'icon_svg'=>'icon_BLOCK.svg', 'sprite_id' => 34, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('list_tag_sets'), 'link' => 'action=listtaggroups', 'icon_svg'=>'icon_SELL.svg', 'sprite_id' => 52, 'condition' => isGeneralAdmin() && CanUseCodeTags()),
                    array('label' => _tk('assign_tags'), 'link' => 'action=listtagfields', 'icon_svg'=>'icon_TAG.svg', 'sprite_id' => 53, 'condition' => isGeneralAdmin() && CanUseCodeTags()),
                    array('label' => _tk('in05'), 'link' => 'action=in05', 'icon_svg'=>'icon_NPSA.svg', 'sprite_id' => 35, 'condition' => (ModIsLicensed('INC') && HasSetupPermissions("INC") && bYN(GetParm('SHOW_NPSA_SETTINGS', 'Y')))),
                    array('label' => _tk('export_nrls_mappings'), 'is_hide_icon' => true, 'link' => 'action=exportnrlsmappings', 'icon_svg'=>'icon_NRLS.svg', 'sprite_id' => 35, 'condition' => (ModIsLicensed('INC') && HasSetupPermissions("INC"))),
                    array('label' => _tk('excel-import-mapping-profiles'), 'link' => 'action=listimportprofiles', 'icon_svg'=>'icon_EXCEL.svg', 'sprite_id' => 43, 'condition' => IsFullAdmin() && bYN(GetParm('EXCEL_IMPORT', 'N'))),
                    array('label' => _tk('import-using-profile'), 'link' => 'service=importexcel&event=setupimport', 'icon_svg'=>'icon_IMPORT_PROFILE.svg', 'sprite_id' => 44, 'condition' => isGeneralAdmin() && bYN(GetParm('EXCEL_IMPORT', 'N'))),
                    array('label' => _tk('action_chains'), 'link' => 'action=listactionchains', 'icon_svg'=>'icon_ACTION.svg', 'sprite_id' => 46,  'condition' => isGeneralAdmin()),
                    array('label' => _tk('task-generator'), 'link' => 'action=listtasks', 'icon_svg'=>'icon_TASK.svg', 'sprite_id' => 47,  'condition' => (isGeneralAdmin() && bYN(GetParm("WEB_TRIGGERS","N")))),
                    array('label' => _tk('cqc_location_setup'), 'link' => 'action=cqclocations', 'icon_svg'=>'icon_CQC_LOCATION.svg', 'sprite_id' => 48,  'condition' => (isGeneralAdmin() && (ModIsLicensed('CQO') || ModIsLicensed('ACR')))),
                    array('label' => _tk('apply_cqc_outcomes'), 'link' => 'action=cqcselectlocations', 'icon_svg'=>'icon_ADD_TEMPLATE.svg', 'icon_blue' => 'images/icon_save_blue.gif', 'sprite_id' => 49, 'condition' => (isGeneralAdmin() && ModIsLicensed('CQO'))),
                    array('label' => _tk('edit_cqc_templates'), 'link' => 'action=listcqctemplates', 'icon_svg'=>'icon_EDIT_TEMPLATE.svg', 'icon_blue' => 'images/icon_save_blue.gif', 'sprite_id' => 50, 'condition' => (isGeneralAdmin() && ModIsLicensed('CQO'))),
                    array('label' => _tk('LOCNamesTitle'), 'link' => 'action=asmlocations', 'icon_svg'=>'icon_LOCATION.svg', 'icon_blue' => 'images/icon_save_blue.gif', 'sprite_id' => 50, 'condition' => (isGeneralAdmin() && (ModIsLicensed('CQO') || ModIsLicensed('ACR')))),
                    array('label' => 'Batch update field set up', 'link' => 'action=excludebatchupdate', 'icon_svg'=>'icon_SETTING.svg', 'icon_blue' => 'images/icon_save_blue.gif', 'sprite_id' => 50, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('automatically_merge_contacts'), 'is_hide_icon' => true, 'link' => $scripturl.'?action=automaticcontactmerge', 'icon_svg'=>'icon_CELL_MERGE.svg', 'external_link' => true, 'icon_blue' => 'images/icon_save_blue.gif', 'sprite_id' => 50, 'custom_class' => 'automatically_merge_contacts', 'condition' => IsFullAdmin() && bYN(GetParm('PERMIT_AUTOMATIC_CONTACT_MERGE', 'N')) && bYN(GetParm('AUTOMATIC_CONTACT_MERGE', 'N'))),
                    array('label' => _tk('setup_ko41_codes'), 'link' => 'action=manageko41ods3lettercodes', 'icon_svg'=>'icon_SETTING.svg', 'icon_blue' => 'images/icon_save_blue.gif', 'sprite_id' => 50, 'condition' => (isGeneralAdmin() && (ModIsLicensed('COM')|| ModIsLicensed('INC')) && (bYN(GetParm("ODS_CODES_ENABLED","N")) || isKO41Admin()))),
                    array('label' => _tk('generate_ko41_report'), 'link' => $KO41Link, 'is_hide_icon' => true, 'external_link' => true, 'icon_svg'=>'icon_REPORT_BAR.svg', 'icon_blue' => 'images/icon_save_blue.gif', 'sprite_id' => 50, 'condition' => $this->registry->getParm('KO41_ENABLE', 'N', false, true)  && $this->registry->getParm('KO41_SERVICE_AREA', '', true) && $this->registry->getParm('KO41_ODS', '', true),'scripturl' => $scripturl),
                    array('label' => _tk('lfpse_auto_population_title'), 'link' => 'action=lfpseautopopulation', 'icon_svg'=>'icon_LFPSE.svg', 'sprite_id' => 50, 'condition' => bYN(GetParm("PSIMS_ENABLED","N")) && isGeneralAdmin() ),
                    array('label' => _tk('lfpse_trigger_mapping'), 'link' => 'action=lfpsetriggermapping', 'icon_svg'=>'icon_LFPSE.svg', 'sprite_id' => 50, 'condition' => bYN(GetParm("PSIMS_ENABLED","N")) && isGeneralAdmin() ),
                ),
            ),
            'software' => array(
                'label' => _t('Software Settings'),
                'contents' => array(
                    array('label' => _tk('software_licensing'), 'link' => 'action=licence', 'icon_svg'=>'icon_KEY.svg', 'sprite_id' => 19, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('send_config_to_datix'), 'link' => 'action=sendconfigxml', 'is_hide_icon' => true, 'icon_svg'=>'icon_SEND_EMAIL.svg', 'sprite_id' => 20, 'condition' => isGeneralAdmin()),
                    array('label' => _tk('user_access_report'), 'link' => 'action=usergroupreport', 'icon_svg'=>'icon_BOOK.svg', 'sprite_id' => 21, 'condition' => IsSubAdmin()),
                    array('label' => _tk('diagnose_system_problems'), 'link' => 'action=diagnosesystemproblems', 'icon_svg'=>'icon_BOOK.svg', 'sprite_id' => 21, 'condition' => isGeneralAdmin() && bYN($this->registry->getParm('DATIX_SUPPORT_ACCOUNT', 'N'))),
                    array('label' => _tk('php8_home_text'), 'link' => 'action=php8stringfix', 'icon_svg'=>'icon_BOOK.svg', 'sprite_id' => 21, 'condition' => isGeneralAdmin() && bYN($this->registry->getParm('SHOW_CLIENT_FOLDER_ERRORS', 'N'))),
                    array('label' => _tk('help_alt'), 'icon' => 'images/icon_help.gif', 'icon_svg'=>'icon_HELP.svg', 'icon_blue' => 'images/icon_help_blue.gif', 'link' => '?action=helpfinder&module=ADM', 'new_window' => true, 'external_link' => true, 'sprite_id' => 51, 'condition' => isGeneralAdmin(), 'id' => 'help')
                ),
            ),
        );

        $HTML = '<div id="landing-page" class="row admin-panel">';

        if (isset($_GET['configSendStatus']))
        {
            switch($_GET['configSendStatus'])
            {
                case 'true':
                    $HTML .= '<script language="javascript" type="text/javascript" nonce="'.$_SESSION["nonce"].'">jQuery(document).ready(function(){alert("Email has been successfully sent")})</script>';
                    break;
                case 'false':
                    $HTML .= '<script language="javascript" type="text/javascript" nonce="'.$_SESSION["nonce"].'">jQuery(document).ready(function(){alert("ERROR: Support email address is not set, no email has been sent")})</script>';
                    break;
            }
        }


        foreach ($AdminLinkArray as $col => $colDetails)
        {
            $keys = array_keys($AdminLinkArray);
            $firstKey = reset($keys);
            $lastKey = end($keys);

            $class = '';
            if ($col === $firstKey) {
                $class .= ' first';
            }
            if ($col === $lastKey) {
                $class .= ' final';
            }
            $HTML .= '<div class="col gu1 panel-section'.$class.'">';
            $HTML .= $this->_renderNavSection($colDetails);
            $HTML .= '</div><!--col-->';
        }

        $HTML .= '</div>';

        return $HTML;
    }

    function _renderNavSection(array $section)
{
    global $scripturl, $JSFunctions;

    $html = '<div class="content content_section">
                <div class="parent-section-header"><h1 class="section-header">' . htmlspecialchars($section['label']) . '</h1></div>
                <ul class="icon-bg section-list">';
    
    foreach ($section['contents'] as $linkDetails) {
    if (!empty($linkDetails['condition'])) {
        if (!array_key_exists('contents', $linkDetails) || !is_array($linkDetails['contents'])) {
            $link = $linkDetails['external_link'] 
                ? $linkDetails['link'] 
                : $scripturl . '?' . $linkDetails['link'];

            $randId = rand();
            $idAttr = !empty($linkDetails['id']) 
                ? 'id="' . htmlspecialchars($linkDetails['id']) . '"' 
                : 'id="link_' . $randId . '"';

            $classAttr = !empty($linkDetails['disabled']) ? ' class="disabled"' : '';
            $targetAttr = !empty($linkDetails['new_window']) ? ' target="_blank"' : '';

            $icon = '<img class="admin_icons" src="Images/icons/' . htmlspecialchars($linkDetails['icon_svg']) . '" alt="icon" />';
            $label = '<span>' . htmlspecialchars($linkDetails['label']) . '</span>';
            $forwardIcon = '<img class="arrow_admin" src="Images/icons/icon_FORWARD_GRAY.svg" alt="icon" />';
            $hideIconClass = $linkDetails['is_hide_icon'] ? "link_text_admin_label" : "text_label";
            $showForwardIcon = empty($linkDetails['is_hide_icon']) ? $forwardIcon : '';

            $html .= '<li>';

            if (!empty($linkDetails['scripturl'])) {
                $html .= '<a class="admin_link" ' . $idAttr . $classAttr . ' href="javascript:void(0);">'
                       . $icon
                       . '<div class="' . $hideIconClass . '">' . $label . $showForwardIcon . '</div></a>';

                $html .= '</li>';
                $html .= '<script nonce="' . $_SESSION["nonce"] . '" type="text/javascript">
                    jQuery(function() {
                        jQuery("body").on("click", "#link_' . $randId . '", function() {
                            ' . $link . ';
                        });
                        jQuery("body").on("click", ".ko41_export", function() {
                            var year = jQuery("input#ko41Year").val();
                            var quarter = jQuery("input#ko41Quarter").val();
                            window.open("' . htmlspecialchars($linkDetails['scripturl']) . '?action=ko41generate&year=" + year + "&quarter=" + quarter, "_self");
                        });
                    });
                </script>';
            } elseif (!empty($linkDetails['custom_class'])) {
                $html .= '<a class="admin_link ' . htmlspecialchars($linkDetails['custom_class']) . '" ' . $idAttr . $classAttr 
                       . ' jsCall="' . htmlspecialchars($link) . '"' . $targetAttr . '>'
                       . $icon
                       . '<div class="' . $hideIconClass . '">' . $label . $showForwardIcon . '</div></a>';
                $html .= '</li>';
            } else {
                $html .= '<a class="admin_link" ' . $idAttr . $classAttr . ' href="' . htmlspecialchars($link) . '"' . $targetAttr . '>'
                       . $icon
                       . '<div class="' . $hideIconClass . '">' . $label . $showForwardIcon . '</div></a>';
                $html .= '</li>';
            }
        }
    }
}


    $html .= '</ul></div><!-- content -->';

    if (isset($linkDetails['contents']) && is_array($linkDetails['contents'])) {
        $html .= '<div class="security_menu">';
        $html .= $this->_renderNavSection($linkDetails);
        $html .= '</div>';
    }

    return $html;
}


    function CheckSetupAllowed()
    {
        $ModArray = getModArray();
        $NotArray = array('DAS','DST','HSA','ADM');

        foreach($ModArray as $code => $name)
        {
            if(HasSetupPermissions($code))
            {
                return true;
            }
        }

        return false;
    }

    /**
     * Generates HTML for accreditation items display
     *
     * @param string $moduleToDisplay The module code to display accreditation items for
     * @return string HTML markup for the accreditation items
    */

    public function getAccreditationItems(string $moduleToDisplay): string
    {
        $menuItems = GetMenuItems(['module' => $moduleToDisplay]);
        $accreditationListing = $this->buildAccreditationListing($menuItems);

        $html = '<div id="landing-page" class="row admin-panel first-panel">';
        $keys = array_keys($accreditationListing);
        $firstKey = reset($keys);
        $lastKey = end($keys);

        foreach ($accreditationListing as $col => $colDetails) {
            $class = ($col === $firstKey ? ' first' : '') . ($col === $lastKey ? ' final' : '');
            $html .= '<div class="col gu1 panel-section' . $class . '">'
                . $this->renderMenuItems($colDetails, $moduleToDisplay)
                . '</div>';
        }
        $html .= '</div>';
        return $html;
    }

    /**
     * Builds a structured listing of accreditation menu items
     *
     * @param array $menuItems Raw menu items to process
     * @return array Processed accreditation listing with standardized structure
    */

    private function buildAccreditationListing(array $menuItems): array
    {
        if (empty($menuItems)) {
            return [];
        }

        $accreditationListing = [];
        foreach ($menuItems as $mainSection) {
            if (!$mainSection || !is_array($mainSection)) {
                continue;
            }
            $menuName = $mainSection['menu_name'] ?? $mainSection['label'] ?? '';
            $menuKey = strtolower(str_replace(' ', '_', trim($menuName)));
            $buildMenuItems = $this->buildMenuItems($mainSection['items']);
            $labelIcon = $this->getFirstIcon($mainSection['items']);

            if (!empty($buildMenuItems)) {
                $accreditationListing[$menuKey] = [
                    'label' => $menuName,
                    'label_icon' => $labelIcon,
                    'show_label_icon' => true,
                    'contents' => $buildMenuItems
                ];
            }
        }
        return $accreditationListing;
    }

    /**
     * Builds menu items array from raw items data
     *
     * @param array|null $items Raw menu items to process
     * @return array Processed menu items with standardized structure
    */

    private function buildMenuItems(?array $items): array
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        $menuItems = [];
        $j = 1;
        
        foreach ($items as $item) {
            $iconSvg = $item['icon'] ? basename($item['icon']) : '';
            $menuItems[] = [
                'label' => $item['label'] ?? '',
                'link' => $item['link'] ?? '',
                'icon_svg' => $iconSvg,
                'sprite_id' => $j,
                'new_window' => $item['new_window'] ?? false,
                'external_link' => $item['external_link'] ?? false,
                'show_icon' => $item['show_icon'] ?? false,
                'hide_forward_icon' => $item['hide_forward_icon'] ?? false,
                'additional_item' => $item['additional_item'] ?? false,
                'condition' => $item['condition'] ?? false
            ];
            $j++;
        }
        return $menuItems;
    }

    /**
     * Get first icon from the menu items
     *
     * @param array|null $items The menu items array
     * @return string The first icon filename or an empty string if no icon is found
    */

    private function getFirstIcon(?array $items): string
    {
        foreach ($items as $item) {
            if (!empty($item['icon'])) {
                return basename($item['icon']);
            }
        }
        return '';
    }

    /**
     * Merges additional menu items into the main menu structure
     *
     * @param array &$menuItems The menu items array to modify
     * @return void
    */

    private function mergeAdditionalMenuItems(array &$menuItems): void
    {
        if (empty($menuItems)) {
            return;
        }

        // Filter additional items
        $additionalMenuItems = array_values(array_filter($menuItems, static function($item) {
            return !empty($item['additional_item']);
        }));

        if (empty($additionalMenuItems)) {
            return;
        }

        // Add additional items flags
        $additionalMenuItems = array_map(static function($item) {
            $item['hide_forward_icon'] = true;
            $item['show_icon'] = true;
            return $item;
        }, $additionalMenuItems);

        // Find a suitable target for merging
        if (isset($menuItems[1]['items']) && is_array($menuItems[1]['items'])) {
            $menuItems[1]['items'] = array_merge($menuItems[1]['items'], $additionalMenuItems);
        }
    }

    /**
     * Renders menu items for a section
     *
     * @param array $section The section containing menu items to render
     * @param string|null $module Optional module identifier
     * @return string HTML markup for the menu items
    */

    private function renderMenuItems(array $section, ?string $module = null): string
    {
        global $scripturl;
        $labelIcon = '';
        
        if (!empty($section['show_label_icon']) && !empty($section['label_icon'])) {
            $labelIcon = '<img class="admin_icons" src="Images/icons/' . htmlspecialchars($section['label_icon']) . '" alt="icon" />';
        }

        $html = '<div class="content content_section">
                    <div class="parent-section-header"><h1 class="section-header">' . $labelIcon . htmlspecialchars($section['label']) . '</h1></div>
                <ul class="icon-bg section-list">';

        foreach ($section['contents'] as $linkDetails) {
            $html .= $this->renderItem($linkDetails, $scripturl);
        }

        $html .= '</ul></div>';

        return $html;
    }

    /**
     * Renders a single item
     *
     * @param array $linkDetails Details of the menu item to render
     * @param string $scripturl Base URL for script links
     * @return string HTML markup for the menu item
    */

    private function renderItem(array $linkDetails, string $scripturl): string
    {
        if (empty($linkDetails['condition']) || 
            (!empty($linkDetails['contents']) && is_array($linkDetails['contents']))) {
            return '';
        }
        
        $html = '';
        $link = !empty($linkDetails['external_link'])
            ? $linkDetails['link']
            : $scripturl . '?' . $linkDetails['link'];

        $randId = mt_rand();
        $idAttr = !empty($linkDetails['id'])
            ? 'id="' . htmlspecialchars($linkDetails['id']) . '"'
            : 'id="link_' . $randId . '"';

        $classAttr = !empty($linkDetails['disabled']) ? ' class="disabled"' : '';
        $targetAttr = !empty($linkDetails['new_window']) ? ' target="_blank"' : '';
        $additionalItemClass = !empty($linkDetails['additional_item']) ? 'additional-item-list' : '';
        $additionalSectionClass = !empty($linkDetails['additional_item']) ? 'additional-section-list' : '';

        $showIcon = (!empty($linkDetails['show_icon']))
            ? '<img class="admin_icons" src="Images/icons/' . htmlspecialchars($linkDetails['icon_svg']) . '" alt="icon" />'
            : '';

        $label = '<span>' . htmlspecialchars($linkDetails['label']) . '</span>';
        $showForwardIcon = empty($linkDetails['hide_forward_icon'])
            ? '<img class="arrow_admin" src="Images/icons/icon_FORWARD_GRAY.svg" alt="icon" />'
            : '';

        $html .= '<li class="'.$additionalSectionClass.'">';
        $html .= '<a class="admin_link ' . $additionalItemClass . '" ' . $idAttr . $classAttr . ' href="' . htmlspecialchars($link) . '"' . $targetAttr . '>'
            . $showIcon
            . '<div class="text_label">' . $label . $showForwardIcon . '</div></a>';
        $html .= '</li>';
        
        return $html;
    }

    public function getAdditionalAccreditationItems(string $moduleToDisplay): string
    {
        $menuItems = GetMenuItems(['module' => $moduleToDisplay]);
        $this->mergeAdditionalMenuItems($menuItems);
        $accreditationListing = $this->buildAccreditationListing($menuItems);

        $html = '<div id="landing-page" class="row admin-panel second-panel">';
        foreach ($accreditationListing as $col => $colDetails) {
            $html .= '<div class="col gu1 panel-section">'
                . $this->renderAdditionalMenuItems($colDetails, $moduleToDisplay)
                . '</div>';
        }
        $html .= '</div>';
        return $html;
    }

    private function renderAdditionalMenuItems(array $section, ?string $module = null): string
    {
        global $scripturl;
        $labelIcon = '';

        $html = '<div class="content content_section">
                <ul class="icon-bg section-list">';

        foreach ($section['contents'] as $linkDetails) {
            if(!empty($linkDetails['additional_item'])) {
                $html .= $this->renderItem($linkDetails, $scripturl);
            }
        }

        $html .= '</ul></div>';

        return $html;
    }
}
?>