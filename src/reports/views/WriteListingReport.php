<?php if ($this->isFullscreen || $this->isDashboard): ?>
    <div class="listing-report-container<?php echo (!empty($this->isDashboard)?' basic_link_csr_js':'');?>"<?php if($this->isDashboard){ echo 'style="cursor:pointer" jsCall="SendTo(\'' . $this->scripturl . '?action=reportdesigner&designer_state=closed&module='.$this->packagedReport->report->module.'&recordid='.$this->recordid.'&from_dashboard='.$this->dashboardId . '\');"'; } ?>>
    <?php if ($this->isFullscreen): ?>
        <table id="listing-title" border="0" align="center" cellspacing="1" cellpadding="0" class="bordercolor" width="100%">
            <tr>
                <td style="text-align:center;">
                    <span style="font-size:20px;font-weight:bold;margin-left:200px;"><?= $this->escape($this->packagedReport->name); ?></span>
                    <span style="float:right; width:200px;"><?= _tk('record_count') ?>: <span id="record_count"></span><span style="display:none; color:red;"><?= _tk('error_retrieving_count')?></span></span>
                </td>
            </tr>
        </table>
    <?php endif; ?>
    <div class="table-container" style="display:none;">
        <table border="0" cellspacing="0" cellpadding="0" class="bordercolor listing-fixed-header">
            <thead>
                <tr valign="top up" class="tableHeader_updated">
                <?php foreach ($this->packagedReport->report->columns as $column): ?>
                    <th class="tableHeader" width="<?= $column->getWidthAsPercentage($this->lineWidth) ?><%"><div class="table_head_data">
                        <a id="listingorder" <?php if (!$column->isSortable()) : ?>class="headerDisabled" <?php endif ?> href="#" data-field="<?= $column->getField(); ?>">
                            <?= $column->getTitleWithSuffix($this->packagedReport->report->module) ?>
                        </div></th>
                <?php endforeach ?>
                </tr>
            </thead>
        </table>
    </div>
<div class="report-viewport table-container" id="report-viewport">
    <?php endif ?>

        <table border="0" width="100%" cellspacing="0" cellpadding="0" class="bordercolor" id="listingtable">
            <thead class="listing-header">
                <tr valign="top" class="tableHeader_updated">
                <?php foreach ($this->packagedReport->report->columns as $column): ?>
                    <th class="tableHeader"><div class="table_head_data">
                        <a id="listingorder" <?php if (!$column->isSortable()) : ?>class="headerDisabled" <?php endif ?> href="#" data-field="<?= $column->getField(); ?>">
                            <?= $column->getTitleWithSuffix($this->packagedReport->report->module) ?>
                        </div></th>
                <?php endforeach ?>
                </tr>
            </thead>
            <?= $this->rows ?>
        </table>

<?php if ($this->isFullscreen || $this->isDashboard): ?>
    </div>
</div>
<?php endif ?>