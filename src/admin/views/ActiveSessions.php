<?php

    echo GetSessionMessages();

    $baseUrl = $this->scriptUrl . '?action=usersessions';
    $ApplicationArray = array("WEB" => "Web Application", "MAIN" => "Main Application");

?>
<?php if(getCount($this->resultArray) > 0): ?>
<div class="table-container" style="margin-top:16px">
<table class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">
  <?php if(!empty($proc_result)): ?>
  <tr>
    <td class="windowbg2" colspan="4"><b>Session Ended</b></td>
  </tr>
  <?php endif ?>
  <tr class="tableHeader_updated">
    <th><div class="table_head_data"><a class="<?php echo (($this->order=='recordid') ? 'sort ' . \UnicodeString::strtolower($this->dir) : '') ?>" href="<?php echo $baseUrl ?>&amp;order=recordid&dir=<?php echo (($this->dir=='ASC') ? 'DESC' : 'ASC') ?>">Session ID</a></div></td>
    <th><div class="table_head_data"><a class="<?php echo (($this->order=='fullname') ? 'sort ' . \UnicodeString::strtolower($this->dir) : '') ?>" href="<?php echo $baseUrl ?>&amp;order=fullname&dir=<?php echo (($this->dir=='ASC') ? 'DESC' : 'ASC') ?>">User</a></div></td>
    <th><div class="table_head_data"><a class="<?php echo (($this->order=='ses_application') ? 'sort ' . \UnicodeString::strtolower($this->dir) : '') ?>" href="<?php echo $baseUrl ?>&amp;order=ses_application&dir=<?php echo (($this->dir=='ASC') ? 'DESC' : 'ASC') ?>">Application</a></div></td>
    <th><div class="table_head_data"><a class="<?php echo (($this->order=='ses_start') ? 'sort ' . \UnicodeString::strtolower($this->dir) : '') ?>" href="<?php echo $baseUrl ?>&amp;orderby=ses_start&dir=<?php echo (($this->dir=='ASC') ? 'DESC' : 'ASC') ?>">Last Active</a></div></td>
    <th class="titlebg"><div class="table_head_data"></div></td>
  </tr>
  <?php foreach($this->resultArray as $row): ?>
  <tr class="listing-row">
    <td class="<?php echo (($this->order=='recordid') ? 'sort ' : '') ?>"><div class="table_head_data"><?php echo $row["recordid"] ?></div></td>
    <td class="<?php echo (($this->order=='fullname') ? 'sort ' : '') ?>"><div class="table_head_data"><?php echo $row["fullname"] ?></div></td>
    <td class="<?php echo (($this->order=='ses_application') ? 'sort ' : '') ?>"><div class="table_head_data"><?php echo $ApplicationArray[$row["ses_application"]] ?></div></td>
    <td class="<?php echo (($this->order=='ses_start') ? 'sort ' : '') ?>"><div class="table_head_data"><?php echo FormatDateVal($row["ses_start"]).' '.date("H:i:s",strtotime($row["real_time"])) ?></div></td>
    <td><div class="table_head_data"><a href="<?php echo $baseUrl ?>&delete_session=<?php echo $row["recordid"] ?>">[end session]</a></div></td>
  </tr>
  <?php endforeach ?>
</table>
  </div>
<?php else: ?>
<div style="text-align:center;border:1px solid #6394bd;padding:10px"><b>There are currently no user sessions logged.</b></div>
<?php endif ?>