/* Size of the plugins fonts */
.ui-widget {
    font-size: 0.8em;
}

/* Datepicker styles */

/* Datepicker color for weekends */
.ui-datepicker-calendar .highlight-weekend a {
    color: #CE2D2F !important;
}

.ui-datepicker .ui-datepicker-header {
    border: none !important;
}

.ui-datepicker-calendar .highlight-weekend a.ui-state-active {
    color: #fff !important;
}

.ui-state-default {
    justify-content: center !important;
    align-items: center !important;
    border: 1px solid transparent !important;
    border-radius: 4px !important;
    background: none !important;
    font-weight: normal !important;
    color: #454545 !important;
}

.ui-state-default:not(.ui-state-highlight):not(.ui-state-active):not(.ui-datepicker-custom-close):not(.ui-datepicker-custom-current):hover {
    border: 1px solid #136C6829 !important;
    background: #fff !important;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    border-radius: 4px !important;
    background: #00736E !important;
    color: #fff !important;
    text-decoration: none !important;
}

.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    border-radius: 4px !important;
    color: #fff !important;
    text-decoration: none !important;
    border: 1px solid #FCDE78 !important;
}

.ui-datepicker-custom-bg-border {
    background-color: #ffffff !important;
    border: 1px solid #889192;
}

.ui-datepicker .ui-datepicker-custom-buttonpane {
    background-image: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    background: #FFF !important;
    background: var(--surface-base, color(display-p3 1 1 1)) !important;
    border: none !important;
}

.ui-datepicker .ui-datepicker-custom-buttonpane button {
    float: right;
    margin: .5em .2em .4em;
    cursor: pointer;
    padding: .2em .6em .3em .6em;
    width: auto;
    overflow: visible;
}

.ui-datepicker .ui-datepicker-custom-buttonpane button.ui-datepicker-current {
    float: left;
}

.ui-datepicker .ui-datepicker-custom-buttonpane button.ui-datepicker-custom-current {
    float: left;
    all: unset;
    height: var(--vertical-small, 20px);
    min-width: 44px;
    justify-content: center;
    align-items: center;
    gap: var(--Tight, 8px);
    border-radius: var(--interaction-border-radius, 6px);
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0;
    color: #1F57BA !important;
    font-family: Geist, sans-serif !important;
    font-size: 12px !important;
    font-style: normal !important;
    font-weight: 500 !important;
    line-height: 1.24 !important;
    text-align: center !important;
    cursor: pointer !important;

    /* Optional for text smoothing */
    font-feature-settings: 'ss01' on, 'ss03' on, 'ss04' on, 'ss06' on, 'ss08' on !important;
}

.ui-datepicker .ui-datepicker-custom-buttonpane button.ui-datepicker-custom-close {
    height: 20px !important;
    min-width: 44p !important;
    padding: 3px 8px !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 8px !important;
    border-radius: 6px !important;
    background-color: #1F57BA !important;
    border: none !important;
    cursor: pointer !important;

    /* Typography */
    color: white !important;
    font-family: Geist, sans-serif !important;
    font-size: 12px !important;
    font-style: normal !important;
    font-weight: 500 !important;
    line-height: 1.24 !important;
    text-align: center !important;
    font-feature-settings: 'ss01' on, 'ss03' on, 'ss04' on, 'ss06' on, 'ss08' on !important;

    /* Remove jQuery UI default styles */
    box-shadow: none !important;
}

.ui-datepicker-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.ui-datepicker .ui-datepicker-title select {
    padding: 2px 6px;
    justify-content: center;
    align-items: center;
    border-radius: 4px !important;
    border: 1px solid var(--border-emphasis, #839192) !important;
    border: 1px solid var(--border-emphasis, color(display-p3 0.5333 0.5686 0.5725));
    background: var(--fds-surface-base, #FFF) !important;
    background: var(--fds-surface-base, color(display-p3 1 1 1));
}

.datepicker-body {
    position: relative;
    top: -10px;
}

.ui-datepicker-custom-buttonpane.ui-widget-content {
    margin-top: -20px;
}


.datepicker-separator {
    height: 1px;
    background-color: #136C6829;
    width: 100%;
    margin: 0 auto;
    position: relative;
}

.datepicker-separator::before {
    content: "";
    position: absolute;
    left: -7px;
    right: -7px;
    top: 0;
    bottom: 0;
    background-color: #ccc;
}

tr.datepicker-separator-row {
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
}

tr.datepicker-separator-row td {
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    line-height: 0 !important;
}

tr.datepicker-separator-row td .datepicker-separator {
    margin-top: -10px !important;
    height: 1px !important;
    background-color: #136C6829 !important;
    width: 100% !important;
}

tr.datepicker-separator-row-bottom {
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
}

tr.datepicker-separator-row-bottom td {
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    line-height: 0 !important;
}

tr.datepicker-separator-row-bottom td .datepicker-separator {
    height: 1px !important;
    background-color: #136C6829 !important;
    width: 100% !important;
}

.ui-datepicker-calendar thead tr {
    padding: 2px 0px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--Border-emphasis, rgba(0, 112, 106, 0.16));
    border-bottom: 1px solid var(--Border-emphasis, color(display-p3 0.0745 0.4235 0.4078 / 0.16));
}

.ui-datepicker-calendar thead th {
    text-align: center;
    font-family: Geist, sans-serif;
    font-size: 12px;
    font-weight: 500;
    color: #4A4A4A;
}

.ui-datepicker thead th span {
    color: #2F3939 !important;
    text-align: center;
    font-feature-settings: 'ss01' on, 'ss04' on, 'ss06' on, 'ss08' on;
    font-family: Geist;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    letter-spacing: -0.12px;
}

.ui-datepicker td {
    width: 31px !important;
    height: 21px !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 10px !important;
    box-sizing: border-box !important;
}

.ui-datepicker th,
.ui-datepicker td {
    background-color: #ffffff !important;
}

.ui-datepicker td.ui-datepicker-other-month a {
    background-color: #ffffff !important;
    color: #828B8B !important;
}

.ui-datepicker-calendar {
    border-collapse: separate !important;
    border-spacing: 4px !important;
}

.ui-datepicker-calendar td {
    padding: 0 !important;
}

.ui-datepicker-calendar td a {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    height: 100% !important;
    border-radius: 4px !important;
    box-sizing: border-box !important;
}

.ui-datepicker td a,
.ui-datepicker td span {
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    color: #000000 !important;
}

.ui-datepicker .ui-state-disabled span {
    background-color: transparent;
    color: #828B8B;
}

.ui-datepicker td a.ui-state-active,
.ui-datepicker td {
    color: #fff !important;
    border: none !important;
}

/* Datepicker styles END */

/* Style the select box */
.ui-datepicker-month {
    color: #14716D; /* Color for the selected value */
    font-weight: bold;
}

/* Firefox-only: change all option colors */
.ui-datepicker-month option {
    color: #14716D;
}


/* Custom styles for datepicker navigation arrows */

.ui-datepicker-prev span,
.ui-datepicker-next span {
    display: none !important;
}


.ui-datepicker-prev {
    background: url(../Images/prev-arrow.svg); no-repeat center;
    background-size: 16px 16px;
}

.ui-datepicker-next {
    background: url(../Images/next-arrow.svg); no-repeat center;
    background-size: 16px 16px;
}

/* Custom styles for datepicker navigation arrows END */

/* 2 */

/*.ui-datepicker-prev span,*/
/*.ui-datepicker-next span {*/
/*    display: none !important;*/
/*}*/

/*.ui-datepicker-prev {*/
/*    position: relative;*/
/*}*/

/*.ui-datepicker-prev::before {*/
/*    content: "«"; !* Left double arrow *!*/
/*    font-size: 18px;*/
/*    color: #333;*/
/*    position: absolute;*/
/*    left: 50%;*/
/*    top: 50%;*/
/*    transform: translate(-50%, -50%);*/
/*}*/

/*.ui-datepicker-next {*/
/*    position: relative;*/
/*}*/
/*.ui-datepicker-next::before {*/
/*    content: "»"; !* Right double arrow *!*/
/*    font-size: 18px;*/
/*    color: #333;*/
/*    position: absolute;*/
/*    left: 50%;*/
/*    top: 50%;*/
/*    transform: translate(-50%, -50%);*/
/*}*/

/* //////////////////////////////////////////////////////////////////////////////////////////////////////////// */

/* Custom styles for datepicker navigation arrows */

/*.ui-datepicker-prev,*/
/*.ui-datepicker-next {*/
/*    position: relative;*/
/*    width: 20px;*/
/*    height: 20px;*/
/*    background-color: transparent;*/
/*    border: none;*/
/*    cursor: pointer;*/
/*}*/

/*!* Hide default span icons *!*/
/*.ui-datepicker-prev span,*/
/*.ui-datepicker-next span {*/
/*    display: none !important;*/
/*}*/

/*!* Replace prev arrow with image *!*/
/*.ui-datepicker-prev::before {*/
/*    content: '';*/
/*    position: absolute;*/
/*    left: 50%;*/
/*    top: 50%;*/
/*    width: 20px;*/
/*    height: 20px;*/
/*    background-image: url(../Images/prev-arrow.svg);*/
/*    background-repeat: no-repeat;*/
/*    background-size: contain;*/
/*    background-position: center;*/
/*    transform: translate(-50%, -50%);*/
/*}*/

/*!* Replace next arrow with image *!*/
/*.ui-datepicker-next::before {*/
/*    content: '';*/
/*    position: absolute;*/
/*    left: 50%;*/
/*    top: 50%;*/
/*    width: 20px;*/
/*    height: 20px;*/
/*    background-image: url(../Images/next-arrow.svg);*/
/*    background-repeat: no-repeat;*/
/*    background-size: contain;*/
/*    background-position: center;*/
/*    transform: translate(-50%, -50%);*/
/*}*/

/*.ui-datepicker-prev.ui-state-disabled::before,*/
/*.ui-datepicker-next.ui-state-disabled::before {*/
/*    opacity: 0.5;*/
/*}*/

/* Custom styles for datepicker navigation arrows END */

/* Style the selectmenu buttons */
/*.ui-selectmenu-button {*/
/*    background-color: white;*/
/*    color: #14716D;*/
/*    border: 1px solid #ccc;*/
/*    border-radius: 4px;*/
/*    padding: 4px 10px;*/
/*    font-size: 14px;*/
/*}*/

/*!* Style the options inside dropdown menu *!*/
/*.ui-selectmenu-menu .ui-menu-item-wrapper {*/
/*    font-size: 14px;*/
/*    padding: 6px 10px;*/
/*}*/

/*!* Highlight selected/hovered option *!*/
/*.ui-selectmenu-menu .ui-menu-item-wrapper.ui-state-active {*/
/*    background-color: #E0F7F6 !important;*/
/*    color: #14716D !important;*/
/*}*/

/*!* Optional: Match calendar text color *!*/
/*.ui-datepicker {*/
/*    font-family: sans-serif;*/
/*}*/