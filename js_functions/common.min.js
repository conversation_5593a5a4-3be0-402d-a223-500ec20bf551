// @ts-nocheck
if(!txt)var txt=new Object;if(!globals)var globals=new Object;var qTips=new Object,spellcheck=new Object,spellcheckButton=null;function isDate(e){var t=/^(\d{1,2})([\-/\.])(\d{1,2})([\-/\.])(\d{2,4})$/;"date"==jQuery(e).attr("type")&&Modernizr.inputtypes.date?t=/^(\d{2,4})(?:[\-])(\d{1,2})(?:[\-])(\d{1,2})$/:"US"==globals.dateFormatCode&&(t=/^(\d{1,2})([\-/.])(\d{1,2})([\-/.])(\d{2,4})$/);var n=e.value.match(t);if(""!=e.value){if(null==n)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if("date"==jQuery(e).attr("type")&&Modernizr.inputtypes.date?(day=n[3],month=n[2],year=n[1]):"US"==globals.dateFormatCode?(day=n[3],month=n[1],year=n[5]):(day=n[1],month=n[3],year=n[5]),4!=year.length||year<1753)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if(month<1||month>12)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if(day<1||day>31)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if((4==month||6==month||9==month||11==month)&&31==day)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if(2==month){var i=year%4==0&&(year%100!=0||year%400==0);if(day>29||29==day&&!i)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1}1==day.length&&(day="0"+day),1==month.length&&(month="0"+month),("date"==jQuery(e).attr("type")&&!Modernizr.inputtypes.date||"date"!=jQuery(e).attr("type"))&&("US"==globals.dateFormatCode?e.value=month+"/"+day+"/"+year:e.value=day+"/"+month+"/"+year)}return!0}function isAmericanDate(e){var t=e.value.match(/^(\d{1,2})([\-/.])(\d{1,2})([\-/.])(\d{2,4})$/);if(""!=e.value){if(null==t)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if(day=t[3],month=t[1],year=t[5],4!=year.length)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if(month<1||month>12)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if(day<1||day>31)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if((4==month||6==month||9==month||11==month)&&31==day)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1;if(2==month){var n=year%4==0&&(year%100!=0||year%400==0);if(day>29||29==day&&!n)return alertWithNoSubmit("You did not enter a valid date",e),e.value="",e.focus(),!1}1==day.length&&(day="0"+day),1==month.length&&(month="0"+month),e.value=month+"/"+day+"/"+year}return!0}function isTime(e){var t=e.value.match(/^([012]?\d)([:.]){0,1}(\d{2})$/);if(""!=e.value){if(null==t)return alertWithNoSubmit("You did not enter a valid time",e),e.value="",e.focus(),!1;if(hours=t[1],minutes=t[3],hours<0||hours>23)return alertWithNoSubmit("You did not enter a valid time",e),e.value="",e.focus(),!1;if(minutes<0||minutes>59)return alertWithNoSubmit("You did not enter a valid time",e),e.value="",e.focus(),!1;1==hours.length&&(hours="0"+hours),e.value=hours+":"+minutes}return!0}function CheckNumber(e){var t=jQuery("#"+e),n=t.val().replace(/ /g,"");return!(""!=n&&!jQuery.isNumeric(n)&&"recordid"!=t[0].name&&"act_cas_id"!=t[0].name&&!t[0].name.includes("UDF_"))||(alertWithNoSubmit("You did not enter a valid number"),t.focus(),t.val(""),!1)}function CheckMoney(e,t){t=setDefaultParameter(t,!1);var n=e.val(),i=globals.currencyChar;if(t){if(new RegExp(/\*{2,}|={3,}|!{2,}|>{2,}|<{2,}|:{2,}|>[^>=]+=|<[^<=]+=|![^!=]+=|=[^=]+=/g).test(n))return e.val(""),notifyInvalidValue(e),!1;n=n.replace(/ |\*|=|!|>|<|:/g,"")}return!(""!=(n=n.replace(new RegExp("\\.|-|,|\\"+i,["g"]),""))&&!jQuery.isNumeric(n))||(e.val(""),notifyInvalidValue(e),!1)}function ValidateCurrency(e,t){var n=e.val().trim(),i=globals.currencyChar;if(t=t||!0,!n&&t)return!0;var r="Please enter a valid currency.";return n==i?(e.val(""),notifyInvalidValue(e,r)):(n=n.replace(new RegExp("\\"+i,["g"]),""),!!(new RegExp(/^(?:[0-9]+,)*[0-9]+(?:\.\d{1,2})?$/).test(n)&&parseFloat(n.replace(/,/,""))>=0)||(r='You did not enter a valid currency with maximum of two decimal points. Please use only numbers and the "." and "," characters',e.val(""),notifyInvalidValue(e,r)))}function notifyInvalidValue(e,t){return alertWithNoSubmit(t=t||"You did not enter a valid value"),e.focus(),!1}function expandIt(whichEl,fld){try{if(fld.checked)document.getElementById(whichEl).style.display="",$("show_section_"+whichEl)&&fld.id!="show_section_"+whichEl&&($("show_section_"+whichEl).value="1");else{try{eval("var sectionshow = CheckSectionShow_"+whichEl+"();")}catch(e){}sectionshow||(document.getElementById(whichEl).style.display="none",$("show_section_"+whichEl)&&fld.id!="show_section_"+whichEl&&($("show_section_"+whichEl).value="0"))}initialiseSpellchecker()}catch(e){}}function expandItYN(whichEl,fld){try{if("Y"==fld.value)document.getElementById(whichEl).style.display="",$("show_section_"+whichEl)&&fld.id!="show_section_"+whichEl&&($("show_section_"+whichEl).value="1");else{try{eval("var sectionshow = CheckSectionShow_"+whichEl+"();")}catch(e){}sectionshow||(document.getElementById(whichEl).style.display="none",$("show_section_"+whichEl)&&fld.id!="show_section_"+whichEl&&($("show_section_"+whichEl).value="0"))}}catch(e){}}var GlobalChangeTracker=!1,AlertAfterChange=!1;function CheckChange(){return!GlobalChangeTracker||!AlertAfterChange||(alert("You must save or cancel the changes you have made to this record before you can access this feature."),!1)}function CheckMax(e,t,n){var i="M"===n?unformatCurrency(e.value):e.value;return!(jQuery.isNumeric(i)&&i>t)||(alertWithNoSubmit("The maximum value allowed is "+t,e),e.focus(),!1)}function CheckMin(e,t,n){var i="M"===n?unformatCurrency(e.value):e.value;return!(jQuery.isNumeric(i)&&i<t)||(alertWithNoSubmit("The minimum value allowed is "+t,e),e.focus(),!1)}function setChanged(e){var t=document.getElementById("CHANGED-"+e);return t&&(t.value="1"),GlobalChangeTracker=!0,!0}var ricoGrids=new Array,sAjaxReturn;function showFormPanel(e){jQuery(".panel").hide(),jQuery("#"+e).show();var t=jQuery("#form-panel");if(t.length&&(panelPattern=/^panel\-(.+)$/i,panelName=panelPattern.exec(e),t.val(panelName[1])),formWidth=null,jQuery("#"+e).find("input[class~=ff_select]").each((function(){var e=jQuery(this);e.length>0&&jQuery.isFunction(e.resizeInput)&&e.resizeInput()})),jQuery("#"+e).find("input[class~=multi_select]").each((function(){var e=jQuery(this);e.length>0&&jQuery.isFunction(e.resizeValuesList)&&e.resizeValuesList()})),ricoGrids)for(var n=0;n<ricoGrids.length;n++)ricoGrids[n].grid&&(ricoGrids[n].grid.sizeDivs(),ricoGrids[n].grid.updateHeightDiv());initialiseSpellchecker()}function moveScreenToField(e,t){if(!e)return!1;var n=$(e);if(t)var i="panel-"+t;n&&($(i)&&showFormPanel(i),n.scrollTo())}function validateEmpty(e,t,n,i){var r=document.getElementById(t);if(n)document.getElementById(n);return null==e||""==e?(r&&(document.getElementById(t.replace("err",""))||document.getElementsByName(t.replace("err","")).length>0)&&(r.style.display="block"),!1):(r&&(r.style.display="none"),document.getElementById("errinc_result")&&document.getElementById("inc_result")&&""!=document.getElementById("inc_result").value&&(document.getElementById("errinc_result").style.display="none"),!0)}function alertWithNoSubmit(e,t){if(document.forms[0]){var n=document.forms[0].onsubmit;document.forms[0].onsubmit=new Function("","return false;"),OldAlert(e),document.forms[0].onsubmit=n}}function setIsetImagemage(e,t){document.getElementById(e).src=t}function renderFields(e,t,n){void 0===n&&(n=null);var i=jQuery(e),r=i.closest(".toggle-group"),a=r.find(".toggle-target"),o=r.find(".toggle-loading");a.data("rendered")||(o.length||(o=jQuery("<div/>",{class:"toggle-loading",text:"Please wait, loading section content..."}).css({display:"none"}),a.before(o)),t.section_name.match("contact")&&checkForMaxSuffix()&&(t.maxSuffixExists=!0),o.slideDown((function(){jQuery.ajax({url:scripturl+"?action=httprequest&type=renderfields",type:"POST",async:!0,data:t}).done((function(t){a.html(t).data("rendered",!0),i.data("render",null),o.slideUp((function(){o.hide()})),initialiseCalendars(),a.find("textarea").limitTextareaChars(),"function"==typeof n&&n(),"function"==typeof lfpseFieldsShowHide&&-1!==e.indexOf("lfpse_")&&-1!==e.indexOf("_section")&&lfpseFieldsShowHide()}))})))}function checkForMaxSuffix(){return jQuery("#max_suffix").length>0}function ToggleFormDesignSection(e){var t,n=jQuery("#"+e+"_title_row"),i=n.find("> table");jQuery.browser.msie&&(7==jQuery.browser.version||6==jQuery.browser.version)&&i.length>0?(i.toggle(),t=i.first().is(":visible")):(n.siblings().toggle(),t=n.siblings("table,li").first().is(":visible")),t?(jQuery("#twisty_image_"+e).attr("src","Images/collapse.gif"),jQuery("#SECTIONEXPANDED-"+e+"-"+e).val("1")):(jQuery("#twisty_image_"+e).attr("src","Images/expand.gif"),jQuery("#SECTIONEXPANDED-"+e+"-"+e).val(""))}function ToggleTwistyExpand(e,t,n,i){"none"!=document.getElementById(e).style.display?(document.getElementById(e).style.display="none",n&&setIsetImagemage(t,n)):(document.getElementById(e).style.display="",i&&setIsetImagemage(t,i))}function getAbsenceDays(e,t){var n=t?"_"+t:"";if($("link_daysaway"+n)){var i,r=document.getElementById("link_abs_start"+n).value,a=document.getElementById("link_abs_end"+n).value;document.getElementById("link_daysaway"+n).value;r&&a&&!1!==(i=days_between(r,a,e))&&($("link_daysaway"+n).value=i)}}function days_between(e,t,n){if(ds_Y=e.substring(6,10),"US"==n?(ds_M=e.substring(0,2),ds_d=e.substring(3,5)):(ds_M=e.substring(3,5),ds_d=e.substring(0,2)),ds_M-=1,date1=new Date(ds_Y,ds_M,ds_d),ds_Y=t.substring(6,10),"US"==n?(ds_M=t.substring(0,2),ds_d=t.substring(3,5)):(ds_M=t.substring(3,5),ds_d=t.substring(0,2)),ds_M-=1,date2=new Date(ds_Y,ds_M,ds_d),date2<date1)return!1;var i=Math.abs(date2.getTime()-date1.getTime());return Math.round(i/864e5)}function addUDFSearch(e,t){var n=t.mod,i=$(e).options[$(e).selectedIndex].value;return new Ajax.Request(scripturl+"?action=httprequest&type=addudfsearchrow&id="+i+"&mod="+n,{method:"get",asynchronous:!1,onSuccess:function(e){var t=SplitJSAndHTML(e.responseText.toString());sAjaxReturn='<div class="fmd_field_div">'+t+"</div>",RunGlobalJavascript()}}),htmlField=document.getElementById("udfRowArray"),htmlField.value+=i+"|",1==document.getElementById("UDFSearchSelect").options.length&&($("UDFTitleRow").innerHTML='<td class="titlebg" colspan="2"><b>Extra Fields</b></td>'),sAjaxReturn}function addUDFType(e,t,n){var i="UDF_"+e+"_"+n;switch(n){case"D":return'<input type="text" class="D_csr" name="'+i+'" id="'+i+'" size="15" /><img class="cal" src="Images/calendar-default.png" border="0" alt="Calendar" style="cursor:pointer" /><input type="hidden" name="CHANGED-'+i+'" id="CHANGED-'+i+'" />';case"M":return'<input type="text" class="M_csr" name="'+i+'" id="'+i+'" size="10" value="" /><input type="hidden" name="CHANGED-'+i+'" id="CHANGED-'+i+'" />';case"N":return'<input type="text" class="N_csr" maxlength="32" size="15" name="'+i+'" value="" /><input type="hidden" name="CHANGED-'+i+'" id="CHANGED-'+i+'" />';case"S":return'<input type="text" class="S_csr" size="15" name="'+i+'" value="" /><input type="hidden" name="CHANGED-'+i+'" id="CHANGED-'+i+'" />';case"Y":return'<select class="Y_csr" name="'+i+'" value="" /><option value="">Choose</option><option value="Y">Yes</option><option value="N">No</option></select><input type="hidden" name="CHANGED-'+i+'" id="CHANGED-'+i+'" />';default:return'<input type="text" name="'+i+'" id="'+i+'" style="width: 200px" /><img class="Df_csr" thisName="'+i+'" data="'+t+"--"+n+"--"+e+'" src="Images/DATIX_Dropdown_n.gif" border="0" width="16" height="16" valign="middle" alt="codes" style="cursor:pointer"  /><input type="hidden" name="CHANGED-'+i+'" id="CHANGED-'+i+'" />'}}function isEmail(e,t){var n=jQuery(e);if(n.val(n.val().replace(/ /g,"")),""!=n.val()){for(var i=n.val().split(","),r=/^(([a-zA-Z0-9\'_-]+(\.))*[a-zA-Z0-9\&\'_-]+|[a-zA-Z0-9\&\'_-]+)@([a-zA-Z0-9_-]{1,63}\.([a-zA-Z0-9_-]+(\.))*[a-zA-Z0-9]+)$/,a=!0,o=0;o<i.length;o++){null==i[o].match(r)&&(a=!1)}return!!a||(t||alertWithNoSubmit("One of the email addresses you entered was not of the correct form.\nIf you are trying to enter multiple addresses, please check you have separated them with a comma",e),setTimeout((function(){n.select()}),globals.deviceDetect.isTablet?500:100),!1)}}function isSingleEmail(e,t){if(e.value=e.value.replace(/ /g,""),""!=e.value){var n=!0;return null==e.value.match(/^(([a-zA-Z0-9\'_-]+(\.))*[a-zA-Z0-9\'_-]+|[a-zA-Z0-9\'_-]+)@([a-zA-Z0-9_-]{1,63}\.([a-zA-Z0-9_-]+(\.))*[a-zA-Z0-9]+)$/)&&(n=!1),!!n||(t||alertWithNoSubmit("The email address you entered was not of the correct form.",e),setTimeout((function(){e.select()}),globals.deviceDetect.isTablet?500:100),!1)}}function log(e){if(!log.window_||log.window_.closed){var t=window.open("",null,"width=400,height=200,scrollbars=yes,resizable=yes,status=no,location=no,menubar=no,toolbar=no");if(!t)return;var n=t.document;n.write("<html><head><title>Debug Log</title></head><body></body></html>"),n.close(),log.window_=t}var i=log.window_.document.createElement("div");i.appendChild(log.window_.document.createTextNode(e)),log.window_.document.body.appendChild(i)}var currentPerson=0;function setClass(e,t){browser.isIE&&browser.version<=7?e.setAttribute("className",t):e.setAttribute("class",t)}function insertInjuryRow(e){SplitJSAndHTML(e.js);var t=e.person_suffix?"_"+e.person_suffix:"",n=document.getElementById("injury_table"+t),i=n.rows.length,r=n.insertRow(i-1),a=r.insertCell(0),o=r.insertCell(1);(a.innerHTML=SplitJSAndHTML(e.cells[0]),o.innerHTML=SplitJSAndHTML(e.cells[1]),null!=e.cells[2])&&(r.insertCell(2).innerHTML=SplitJSAndHTML(e.cells[2]));setClass(r,"windowbg2"),r.setAttribute("id","injury_row"+t+"_"+e.row_suffix);var l="true";1!=$("RETAIN_COMBO_CHILDREN").value&&(l="false"),TempJS="\r\n function OnChange_injury"+t+"_"+e.row_suffix+"() {jQuery('#bodypart"+t+"_"+e.row_suffix+"_title').checkClearChildField("+l+");}",JavascriptToExecute.push(TempJS),RunGlobalJavascript()}function insertPropertyRow(e){SplitJSAndHTML(e.js);var t=e.person_suffix?"_"+e.person_suffix:"",n=document.getElementById("property_table"+t),i=n.rows.length,r=n.insertRow(i-1);(r.insertCell(0).innerHTML=SplitJSAndHTML(e.cells[0]),r.insertCell(1).innerHTML=SplitJSAndHTML(e.cells[1]),r.insertCell(2).innerHTML=SplitJSAndHTML(e.cells[2]),null!=e.cells[3])&&(r.insertCell(3).innerHTML=SplitJSAndHTML(e.cells[3]));null!=e.cells[4]&&(r.insertCell(4).innerHTML=SplitJSAndHTML(e.cells[4]));setClass(r,"windowbg2 property_row"),r.setAttribute("id","property_row"+t+"_"+e.row_suffix),RunGlobalJavascript()}function deleteInjuryRow(e,t){var n=""!=t&&0!=t?"_"+t:"",i=$("injury_row"+n+"_"+e).rowIndex;i&&document.getElementById("injury_table"+n).deleteRow(i)}function deletePropertyRow(e,t){var n=""!=t&&0!=t?"_"+t:"",i=$("property_row"+n+"_"+e).rowIndex;i&&document.getElementById("property_table"+n).deleteRow(i)}var dif1_section_suffix=new Object,currentElement,currentElementInsertAfter,copyfromsuffix,copytosuffix;function writeSuffixLimits(){for(i in dif1_section_suffix)if($(i+"_max_suffix"))$(i+"_max_suffix").value=dif1_section_suffix[i];else{var e=document.createElement("input");e.type="hidden",e.value=dif1_section_suffix[i],e.name=i+"_max_suffix",e.id=i+"_max_suffix",document.forms[0].appendChild(e)}}function isVisible(e){for(;e&&"body"!=e.tagName;){if(e.style&&"none"==e.style.display)return!1;e=e.parentNode}return!0}function insertAfter(e,t,n){e.insertBefore(t,n.nextSibling)}function AddSectionToForm(e,t,n,i,r,a,o,l,s){var c,u;if($(e+"_max_suffix")){if(c=u=Number($(e+"_max_suffix").getValue()),e.indexOf("_")>0)for(var d=e.split("_"),p=d.length-1;p>-1&&!isNaN(d[p]);p--)c=d[p]+"_"+c}else c=u=dif1_section_suffix[e];n&&(currentElement=n),currentElementInsertAfter=a||!1,r||(r="");var f="suffix="+c+"&type="+e+"&subtype="+t+"&module="+i+(jQuery("input[name=formlevel]").length?"&level="+jQuery("input[name=formlevel]").val():"")+(null!=l?"&form_id="+l:"")+r;"A"==t&&"contact"==e&&"INC"==i&&(f=f+"&"+jQuery.param(outputFormDataObject("New"))),new Ajax.Request(scripturl+"?action=httprequest&type=adddynamicsection",{method:"post",postBody:f,onSuccess:function(t){var n=currentElement.parentNode;if("recipients"==e||"filter"==e)var i=document.createElement("li");else i=document.createElement("div");i.id=e+"_section_div_"+u,currentElementInsertAfter?insertAfter(n,i,currentElement):n.insertBefore(i,currentElement),InsertSectionIntoForm(t,i),initialiseSpellchecker(),null!=s&&s()}}),$(e+"_max_suffix")?$(e+"_max_suffix").setValue(u+1):dif1_section_suffix[e]++}function CopySectionToForm(e,t,n,i,r,a,o,l){copyfromsuffix=r,copytosuffix=dif1_section_suffix[e],currentElement=n,currentElementInsertAfter=a;var s="type="+e+"&subtype="+t+"&module="+i;new Ajax.Request(scripturl+"?action=httprequest&type=getrowlist&responsetype=json",{method:"post",postBody:s,onSuccess:function(n){for(var r=n.responseText.split(","),a="",s=0;s<r.length;s++){var c=jQuery("[name="+r[s]+"_"+copyfromsuffix+"]");a+=c.is(":radio")?"&"+r[s]+"="+c.filter((function(){return null!=jQuery(this).val()&&"checked"==jQuery(this).attr("checked")})).val():"&"+r[s]+"="+c.val()}AddSectionToForm(e,t,currentElement,i,a,currentElementInsertAfter,o,l)}})}function InsertSectionIntoForm(e,t){var n=SplitJSAndHTML(e.responseText.toString());jQuery(t).html(n),RunGlobalJavascript()}function AddHiddenField(e){var t=document.createElement("input");t.type="hidden",t.value=e.value,t.name=e.name,t.id=e.id,e.parentNode.appendChild(t)}function AddCustomHiddenField(e,t,n){var i=document.createElement("input");i.type="hidden",i.value=n,i.name=t,i.id=e,document.forms[0].appendChild(i)}function ReplaceSection(e,t,n,i,r,a){var o="suffix="+t+"&type="+n+"&subtype="+i+"&module="+e+"&clearsection=1&level="+jQuery("input[name=formlevel]").val()+"&parent_form_id="+a;new Ajax.Request(scripturl+"?action=httprequest&type=adddynamicsection",{method:"post",postBody:o,onSuccess:function(e){$(n+"_section_div_"+t).innerHTML="",InsertSectionIntoForm(e,$(n+"_section_div_"+t)),initialiseSpellchecker()}})}var JavascriptToExecute=new Array;function RunGlobalJavascript(e){null==typeof e&&(e=null),AppendJavascript(JavascriptToExecute,e)}function SplitJSAndHTML(e,t){var n=new Array,i="";if(null==t&&(t=/<script(?: (?:language="JavaScript"|type="text\/javascript"|src="([^"]+)"))+\s*>([\s\S.]*?)<\/script>/gim),t instanceof RegExp){for(var r;r=t.exec(e);)n.push(r[1]?r[1]:!!r[2]&&r[2]);i=e.replace(t,"");for(var a=0;a<n.length;a++)JavascriptToExecute.push(n[a])}else{i+=(n=e.split(t))[0];for(a=1;a<n.length;a++)TempArray2=n[a].split("<\/script>"),JavascriptToExecute.push(TempArray2[0]),i+=TempArray2[1]}return i}function AppendJavascript(javascriptArray,callback){null==typeof callback&&(callback=null);var loadScript=javascriptArray.shift();if(null!=loadScript)if(/^[a-z0-9\/\._-]*\.js(?:\?v=[0-9]+)?$/i.test(loadScript)){isSrc=!0;var thisScript=document.createElement("script");thisScript.async=!0,thisScript.onload=function(){AppendJavascript(javascriptArray,callback)},thisScript.src=loadScript,document.head.appendChild(thisScript)}else script=document.createElement("script"),script.text=loadScript,script.type="text/javascript",script.setAttribute("nonce",globals.NONCE),head=document.getElementsByTagName("head").item(0),head.appendChild(script),script=null,eval(loadScript),AppendJavascript(javascriptArray,callback);else"function"==typeof callback&&callback.call(this);JavascriptToExecute=[]}function addInjury(e,t,n,i){personSuffix="",currentPerson="",""!=t&&0!=t&&(currentPerson=t,personSuffix="_"+t),nextRow=e.getAttribute("nextSuffix")||"1";for(var r="",a=parseInt(nextRow)+1,o=0;0==o&&a>0;)a--,$("injury"+personSuffix+"_"+a)&&(r=$("injury"+personSuffix+"_"+a).value,o=1);""!=r||0==a?(e.disabled=!0,new Ajax.Request(scripturl+"?action=httprequest&type=insertinjuryrow&"+(0!=currentPerson?"person_suffix="+currentPerson:"")+"&row_suffix="+nextRow+"&linktype="+n+(null!=i?"&form_id="+i:"")+"&responsetype=json",{method:"get",asynchronous:!1,onSuccess:function(e){insertInjuryRow(e.responseText.evalJSON())}}),e.disabled=!1,e.setAttribute("nextSuffix",parseInt(nextRow)+1)):alert("Please select a valid injury before adding a new row")}function addAbsence(e,t){var n=jQuery("tr[name^='absence_row_']").length,i=n-1,r=jQuery("[id=absence_row_"+i+"]"),a=jQuery("[id=absence_start_"+i+"]").val(),o=jQuery("[id=absence_end_"+i+"]").val();void 0!==a&&""!=a&&void 0!==o&&""!=o&&days_between(a,o,globals.dateFormatCode)>0?(e.disabled=!0,new Ajax.Request(scripturl+"?action=httprequest&type=insertabsencerow&nextsuffix="+n+"&formType="+t,{method:"get",asynchronous:!1,onSuccess:function(e){r.after(e.responseText)},onError:function(e){alert("Can not create new row, please try again later.")}}),initialiseCalendars(),e.disabled=!1):void 0!==a&&""!=a&&void 0!==o&&""!=o?alert("Start date must precede end date"):alert("Please fill in the dates before adding a new row")}function addProperty(e,t,n,i){personSuffix="",currentPerson="",""!=t&&0!=t&&(currentPerson=t,personSuffix="_"+t),nextRow=e.getAttribute("nextSuffix")||"1";for(var r="",a=parseInt(nextRow)+1,o=0;0==o&&a>0;)a--,$("ipp_description"+personSuffix+"_"+a)&&(r=$("ipp_description"+personSuffix+"_"+a).value,o=1);if(""!=r||0==a){e.disabled=!0;var l=scripturl+"?action=httprequest&type=insertpropertyrow&"+(0!=currentPerson?"person_suffix="+currentPerson:"")+"&row_suffix="+nextRow+"&linktype="+n+"&responsetype=json&"+($$("input[name=form_id]")[0]?"form_id="+$$("input[name=form_id]")[0].value:"");""!=(i=i||"")&&(l+="&mandatoryFields="+i),new Ajax.Request(l,{method:"get",asynchronous:!1,onSuccess:function(e){insertPropertyRow(e.responseText.evalJSON())}}),e.disabled=!1,e.setAttribute("nextSuffix",parseInt(nextRow)+1)}else alert("Please add a description before adding a new row")}function getElementsByName_iefix(e,t){var n=document.getElementsByTagName(e),r=new Array;for(i=0,iarr=0;i<n.length;i++)att=n[i].getAttribute("name"),att==t&&(r[iarr]=n[i],iarr++);return r}function ToggleInnerHTML(e,t,n){e.innerHTML==t?e.innerHTML=n:e.innerHTML=t}function getFirstNonNull(e){for(var t=0;t<e.length;t++)if(e[t])return e[t];return 0}function getElementPosition(e){var t=curtop=0;if(e.offsetParent)for(t=e.offsetLeft,curtop=e.offsetTop;e=e.offsetParent;)t+=e.offsetLeft,curtop+=e.offsetTop;return[t,curtop]}function Browser(){var e,t,n;this.isFirefox=!1,this.isSafari=!1,this.isIE=!1,this.isNS=!1,this.isOpera=!1,this.version=null;var i=(e=navigator.userAgent).match(/rv:([0-9]+\.[0-9]+)/);if("Mozilla"===navigator.appCodeName){var r=e.match(/Safari\/([0-9.]*)/);return r?(this.isSafari=!0,void(this.version=r[1])):(this.isFirefox=!0,void(this.version=parseFloat(i[1])))}return null!==i&&2===i.length?(this.isIE=!0,void(this.version=parseFloat(i[1]))):(t="MSIE",(n=e.indexOf(t))>=0?(this.isIE=!0,void(this.version=parseFloat(e.substr(n+t.length)))):(t="Netscape6/",(n=e.indexOf(t))>=0?(this.isNS=!0,void(this.version=parseFloat(e.substr(n+t.length)))):(t="Gecko",(n=e.indexOf(t))>=0?(this.isNS=!0,void(this.version=6.1)):(t="Opera/",(n=e.indexOf(t))>=0?(this.isOpera=!0,void(this.version=parseFloat(e.substr(n+t.length)))):void 0))))}function getWindowHeight(){var e=0;return"number"==typeof window.innerHeight?e=window.innerHeight:document.documentElement&&document.documentElement.clientHeight?e=document.documentElement.clientHeight:document.body&&document.body.clientHeight&&(e=document.body.clientHeight),e}function getViewPortSize(){var e=new Array;return e[0]=0,e[1]=0,"number"==typeof window.innerWidth?(e[0]=window.innerWidth,e[1]=window.innerHeight):document.documentElement&&document.documentElement.clientWidth?(e[0]=document.documentElement.clientWidth,e[1]=document.documentElement.clientHeight):document.body&&document.body.clientWidth&&(e[0]=document.body.clientWidth,e[1]=document.body.clientHeight),e}function SetGlobal(e,t){new Ajax.Request(scripturl+"?action=httprequest&type=setglobal&global="+e+"&value="+t,{method:"get"})}function ForceSetGlobal(e,t){new Ajax.Request(scripturl+"?action=httprequest&type=setglobal&global="+e+"&value="+t+"&update_session=1",{method:"get"})}function SetUserParm(e,t,n){jQuery.get(scripturl+"?action=httprequest&type=setglobal&global="+e+"&value="+t+"&user="+n)}var PopupDivs=new Array,ActivePopupDivs=0;function AddNewFloatingDiv(e){return e&&!jQuery.grep(PopupDivs,(function(t){return t.id===e})).length&&PopupDivs.push(new FloatingWindow(e,PopupDivs.length)),GetFloatingDiv(e)}function GetFloatingDiv(e){if(jQuery("#floating_window_numkey_"+e)&&PopupDivs[jQuery("#floating_window_numkey_"+e).val()])return PopupDivs[jQuery("#floating_window_numkey_"+e).val()]}function PopupDivFromURL(e,t,n,i,r,a,o,l){o=setDefaultParameter(o,null),l=setDefaultParameter(l,null),new Ajax.Request(n,{method:"post",postBody:i,asynchronous:!1,onSuccess:function(n){AddNewFloatingDiv(e);var i=GetFloatingDiv(e),s=SplitJSAndHTML(n.responseText.toString());i.setContents(s),i.setTitle(t);0==r.length&&(r=new Array).push({value:"Close",class:"closethis__"+e}),i.setButtons(r),a&&(s.length>2e3&&(a="500px"),i.setWidth(a)),o&&(i.OnBeforeCloseAction=o),l&&(i.OnCloseAction=l),i.display(),RunGlobalJavascript()}}),"ko41export"==e&&filterAvailableYear()}function LimitTextareaChars(e,t){var n=jQuery(e),i=n.val().replace(/(\r\n|\r|\n)/g,"||");if(i.length>t){var r=i.replace(/(^\s+|\s+$)/g,"").slice(0,t).replace(/\|\|/g,"\r\n").replace("|","").replace(/(^\s+|\s+$)/g,"");globals.WEB_SPELLCHECKER&&n.hasClass("spellcheck")?n.closest(".field_input_div").find(".livespell_textarea").text(r):n.val(r),OldAlert("You have reached the character limit set for this field")}}function getValuesArray(id){if(id){eval('var Field = $("'+id+'");');var Values=new Array;if(Field)if("SELECT"==Field.tagName)if(Field.multiple)for(var i=0;i<Field.options.length;i++)Values.push(Field.options[i].value);else Values.push(Field.options[Field.selectedIndex].value);else"INPUT"==Field.tagName&&("text"==Field.type?Values=Field.value.split("|"):"checkbox"==Field.type?Field.checked&&Values.push("Y"):"hidden"==Field.type?Values=Field.value.split(" "):"radio"==Field.type?Values.push(jQuery("input[name="+id+"]:checked").val()):Values.push(Field.value));else jQuery("#"+id+"_title").length&&jQuery("#"+id+"_title").data("search")?Values=jQuery("#"+id+"_title").val().split("|"):jQuery("input[name="+id+"]").length&&"radio"==jQuery("input[name="+id+"]").first().attr("type")&&Values.push(jQuery("input[name="+id+"]:checked").val());return Values}return!1}function getPrintURL(){var e=window.location.href;return-1!=e.search("#panel")?newURL=e.replace(/#panel/,"&print=1#panel"):newURL=e+"&print=1",newURL}function FieldChanged(e){disableAllButtons(),CascadeCheckedFields=new Array,setChanged(e);functionToCall="OnChange_"+e,window[functionToCall]&&window[functionToCall](),TriggerOnChange(e),enableAllButtons()}function SetDisplayDivSize(e){e.style.width="auto",(""==e.innerHTML||e.offsetWidth<200)&&(e.style.width="200px")}Array.prototype.inArray=function(e){var t,n;for(t=0;t<this.length;t++)for(n=0;n<e.length;n++)if(this[t]===e[n])return!0;return!1};var CascadeCheckedFields=new Array;function TriggerOnChange(e){if(-1==CascadeCheckedFields.indexOf(e)){CascadeCheckedFields.push(e);var t=null;t="CascadeCheckState_"+e,window[t]&&window[t](),t=null,t="OnChange_Extra_"+e,window[t]&&window[t](),"function"==typeof lfpseFieldsShowHide&&-1!==e.indexOf("lfpse_")&&lfpseFieldsShowHide()}}function TriggerOnChangeSection(section){eval("var functionExists = window.OnSectionChange_"+section+";"),functionExists&&eval("OnSectionChange_"+section+"();")}function ShowSection(e){$(e)&&($(e).style.display=""),$("show_section_"+e)&&($("show_section_"+e).value="1"),jQuery("#"+e).find(".toggle-target").data("rendered")&&TriggerOnChangeSection(e)}function HideSection(e){$(e)&&($(e).style.display="none"),$("show_section_"+e)&&($("show_section_"+e).value="0"),TriggerOnChangeSection(e)}function ShowFieldRow(e){$(e+"_row")&&($(e+"_row").style.display=""),$("show_field_"+e)&&($("show_field_"+e).value="1"),TriggerOnChange(e)}function HideFieldRow(e){$(e+"_row")&&($(e+"_row").style.display="none"),$("show_field_"+e)&&($("show_field_"+e).value="0"),TriggerOnChange(e)}function FieldIsAvailable(e){var t=jQuery("#show_field_"+e),n=jQuery("#"+e),i=jQuery("#show_section_"+FieldSection[e]);return!(t||!n.length||""==n.val()||"1"!=i.val())||!(!t||"1"!=t.val()||"1"!=i.val())}function removeSelectedItemsFromMultiListbox(e,t){if(eleMultiListbox=$(e),-1!=eleMultiListbox.selectedIndex){for(;-1!=eleMultiListbox.selectedIndex;){eleMultiListbox.options[eleMultiListbox.selectedIndex].value,eleMultiListbox.options[eleMultiListbox.selectedIndex].text;eleMultiListbox.options[eleMultiListbox.selectedIndex]=null}setChanged(eleMultiListbox.name.replace("[]","")),FieldChanged(e)}if(checkMultiWidth(eleMultiListbox),t){var n="";jQuery("#"+e).find("option").each((function(e,t){n=jQuery(t).text().replace(/^\d+:\s/,""),jQuery(t).text(e+1+": "+n)}))}}function editSelectedItemFromMultiListbox(e){var t=jQuery("select#"+e).val(),n=jQuery("#"+e).prop("selectedIndex"),i=[];null!=t?t.length>1?OldAlert("You can only select a single value to edit"):(i[0]={value:"Edit",jsCall:"jQuery(jQuery('#"+e+"').find('option')["+n+"]).prop('value', jQuery('#code').val()).text('"+(n+1)+": '+jQuery('#code').val());GetFloatingDiv('edit_multicode_value').CloseFloatingControl();",class:"basic_link_csr_js"},i[1]={value:"Cancel",jsCall:"GetFloatingDiv('edit_multicode_value').CloseFloatingControl();",class:"basic_link_csr_js"},PopupDivFromURL("edit_multicode_value","Edit Code","index.php?action=editmulticodevalue&token="+token,"fieldName="+e+"&value="+t[0],i)):OldAlert("Please select a value to edit")}function urlencode(e){return e=encodeURIComponent(e)}function SendFeedbackEmail(e,t){var n,i,r="",a="",o=$("fbk_to"),l=$("ajax_email_status"),s=$("fbk_gab"),c=$("fbk_email"),u=$("fbk_html"),d="";if($("feedback_btn").disabled=!0,l.innerHTML='<img src="Images/SendMail.png" /><p>Sending Feedback',l.show(),null!=o)if(o.options)for(o.options.length>0&&(r=o.options[0].value),n=1;n<o.options.length;n++)r+=","+o.options[n].value;else r=""!=o.value?o.value:"";if(null!=s)if(s.options)for(s.options.length>0&&(a=s.options[0].value),n=1;n<s.options.length;n++)a+=","+s.options[n].value;else""!=s.value?a+=s.value:a="";i=c?c.value:"";var p=urlencode($("fbk_subject").value),f=urlencode($("fbk_body").value);if(c&&(c.value=""),null!=o){if(o.length)for(;o.length>0;)o.remove(0);jQuery("#fbk_to_values > ul").children()&&jQuery.each(jQuery("#fbk_to_values > ul").children(),(function(e,t){jQuery(t).remove()}))}if(null!=s){if(s.length)for(;s.length>0;)s.remove(0);jQuery("#fbk_gab_values > ul").children()&&jQuery.each(jQuery("#fbk_gab_values > ul").children(),(function(e,t){jQuery(t).remove()}))}var h=$("fbk_attachments");if(null!=h)if(h.options)for(h.options.length>0&&(d=h.options[0].value),n=1;n<h.options.length;n++)d+=","+h.options[n].value;else""!=h.value?d+=h.value:d="";new Ajax.Request("index.php?action=httprequest&type=sendfeedback",{method:"post",postBody:"module="+e+"&recordid="+t+"&to="+r+"&subject="+p+"&body="+f+"&fbk_email="+i+"&fbk_gab="+a+"&is_html="+u.value+"&attachments="+d,asynchronous:!1,onSuccess:function(e){$("ajax_email_status").innerHTML=e.responseText,$("CHANGED-fbk_body").value="",$("feedback_btn").disabled=!1}})}function completeActionPost(e,t,n,i,r){var a={act_progress:n,id:e,module:i};jQuery.ajax({url:scripturl+"?action=httprequest&type=actioncomplete&responsetype=json",type:"POST",data:a,success:function(n){null!=r?SendTo(r):(jQuery(t).html(n),jQuery(t).next("input#btn_complete_"+e).remove())},error:function(e,t,n){alert("Error: Action could not be updated")}})}var browser=new Browser,FieldSection=new Array;function isIE6(){return browser.isIE&&6==browser.version}function XMLIsValid(e){var t="";return browser.isIE?t=e.xmlDocument.xml:(serializer=new XMLSerializer,t=serializer.serializeToString(e.xmlDocument)),""!=t&&null==t.match("<parsererror")}var StatusesNoMandatory=new Array,mandatoryArray=new Array,mandatoryArraySpecial=new Array;function removeDuplicateMandatoryFields(){for(var e,t=new Array,n=0;n<mandatoryArray.length;n++){e=!1;for(var i=0;i<t.length;i++)t[i][0]==mandatoryArray[n][0]&&t[i][1]==mandatoryArray[n][1]&&(e=!0);e||t.push(mandatoryArray[n])}return t}mandatoryArraySpecial.REJECT=new Array;var windowopen=!1;function CreateCodeList(field,normal){if(1!=windowopen||!normal){windowopen=!0;var NewControl=new CodeSelectionCtrl;ControlIndex=GlobalCodeSelectControls.length,NewControl.setIndex(ControlIndex),GlobalCodeSelectControls.push(NewControl),eval("var isFunc = (typeof CreateCodeList_"+field+" == 'function')"),isFunc?eval("CreateCodeList_"+field+"(ControlIndex, normal)"):windowopen=!1}}function oc(e){for(var t={},n=0;n<e.length;n++)t[e[n]]="";return t}function CheckComplaintDates(){return!(!($("lcom_dack")&&""!=$("lcom_dack").value||$("lcom_dactioned")&&""!=$("lcom_dactioned").value||$("lcom_dresponse")&&""!=$("lcom_dresponse").value||$("lcom_dholding")&&""!=$("lcom_dholding").value||$("lcom_dreplied")&&""!=$("lcom_dreplied").value)||!$("CHANGED-lcom_dreceived")||"1"!=$("CHANGED-lcom_dreceived").value)}function AddNewMessageBox(e){var t,n=parseInt($("max"+e+"Id").value)+1;t='<div><textarea rows="6" cols="60" id="'+e+"_"+n+'" name="'+e+"_"+n+'" ></textarea><div>';var i=document.createElement("div");i.innerHTML=t,$(e+"Div").appendChild(i),$("max"+e+"Id").value=n}var FeedbackValidationNeeded=!1;function DoFeedbackValidation(e,t){var n=new Array;return $("fbk_to")&&$("fbk_to").length>0||$("fbk_email")&&""!=$("fbk_email").value||$("fbk_gab")&&$("fbk_gab").length>0?(n.push({value:txt.btn_send_and_save,jsCall:"SendFeedbackEmail('"+e+"',"+t+");writeSuffixLimits();document.forms[0].submit()",class:"basic_link_csr_js ajax_email_click_disable"}),n.push({value:txt.btn_save_without_sending,jsCall:"writeSuffixLimits(); document.forms[0].submit()",class:"basic_link_csr_js ajax_email_click_disable"}),n.push({value:txt.btn_feedback_cancel,jsCall:"feedbackCancel()",class:"basic_link_csr_js"}),DivAlert("Feedback",txt.fbk_email_check_recipients,n,null),!1):!($("CHANGED-fbk_body")&&"1"==$("CHANGED-fbk_body").value||$("CHANGED-fbk_subject")&&"1"==$("CHANGED-fbk_body").value)||(n.push({value:txt.btn_save_without_sending,jsCall:"writeSuffixLimits();document.forms[0].submit()",class:"basic_link_csr_js"}),n.push({value:txt.btn_feedback_cancel,jsCall:"feedbackCancel()",class:"basic_link_csr_js"}),DivAlert("Feedback",txt.fbk_email_check_amend,n,null),!1)}function feedbackCancel(){GetFloatingDiv("alert"+(PopupDivs.length-1)).CloseFloatingControl(),hideLoadPopup(),enableAllButtons()}function SendFeedback(e,t){selectAllMultiCodes();var n=jQuery("#fbk_to"),i=jQuery("#fbk_to_values > ul").children().length,r=jQuery("#fbk_gab"),a=jQuery("#fbk_gab_values > ul").children().length,o=jQuery("#fbk_email"),l=!1,s=!1;o&&""==o.val()&&n&&i<=0&&r&&a<=0&&(s=!0),s?alert(txt.email_address_missing_err):((o.val()&&isEmail(o,!0)||n&&i>0||r&&a>0)&&(l=!0),l&&SendFeedbackEmail(e,t))}function UncheckMandatoryFields(e,t){for(var n=$(t).select("input[name^=MANDATORY-"+t+"]"),i=0;i<n.length;i++)e.checked?(n[i].writeAttribute("checked",""),n[i].writeAttribute("disabled","disabled")):n[i].writeAttribute("disabled","")}function sendOverdueEmails(e){if($(e+"_overdue_progress_bar").update("Please wait while emails are computed and sent..."),$(e+"_overdue_progress").update(""),"INC"==e)var t=getValuesArray("OVERDUE_EMAIL_STATUS").join("|"),n=getValuesArray("OVERDUE_EMAIL_USERS").join("|");else if("COM"==e)t=getValuesArray("OVERDUE_EMAIL_STATUS_COM").join("|"),n=getValuesArray("OVERDUE_EMAIL_USERS_COM").join("|");new Ajax.Request(scripturl+"?action=httprequest&type=getoverdueemailrecipients&statuses="+t+"&users="+n+"&module="+e+"&responsetype=json",{method:"get",onSuccess:function(t){var n=t.responseText.evalJSON();if(n.contacts&&0!=n.contacts.length){$(e+"_overdue_progress_bar").insert('Sending emails: <span id="'+e+'_overdue_progress_percent">0</span>%');for(var i=0;i<n.contacts.length;i++){var r=n.contacts[i].email,a=n.contacts[i].records.join("|"),o=Math.round(1/n.contacts.length*100),l=i==n.contacts.length-1?1:0;new Ajax.Request(scripturl+"?action=httprequest&type=sendoverdueemails&responsetype=json&email="+r+"&module="+e+"&records="+a+"&percent="+o+"&last="+l,{method:"get",onSuccess:function(t){var n=t.responseText.evalJSON();$(e+"_overdue_progress").insert(n.html);var i=parseInt($(e+"_overdue_progress_percent").innerHTML)+parseInt(n.get.percent);$(e+"_overdue_progress_percent").update(i),"1"==n.get.last&&$(e+"_overdue_progress_bar").update("All overdue email messages have been processed:")}})}}else $(e+"_overdue_progress_bar").update("No emails sent")}})}function checkProfileSave(e){var t=new Array;if(!e){var n=getValuesArray("sta_profile")[0];""!=n&&$("sta_profile")||(n="0");var i=$("sta_profile_old").value;""==i&&(i="0")}e||parseInt(i)!=parseInt(n)&&"0"!=parseInt(n)?(t.push({value:"Yes",jsCall:"jQuery('#clearUserSettings').val('1');continueSaveProfile();",class:"basic_link_csr_js"}),t.push({value:"No",jsCall:"jQuery('#clearUserSettings').val('');continueSaveProfile();",class:"basic_link_csr_js"}),DivAlert("Clear user settings?",txt.profile_clear_check,t,(function(){continueSaveProfile()}))):(jQuery("#clearUserSettings").val(""),continueSaveProfile())}function continueSaveProfile(){selectAllMultiCodes(),submitClicked=!0,document.forms[0].submit()}function disableAllButtons(){for(var e=0;e<$$("input.button").length;e++)$$("input.button")[e].disabled=!0}function enableAllButtons(){for(var e=0;e<$$("input.button").length;e++)$$("input.button")[e].disabled=!1}function displayLoadPopup(e){if(e=setDefaultParameter(e,txt.save_wait_msg),null==GetFloatingDiv("loading_popup")){var t=AddNewFloatingDiv("loading_popup"),n='<div style="width:200px; text-align:center; overflow:hidden;">'+e+"</div>";(new Image).src="Images/loading.gif",n+='<img src="Images/loading.gif" style="margin-left:90px; margin-top:10px;" />',t.setContents(n),t.hideCloseControl(),t.display()}}function hideLoadPopup(){var e=GetFloatingDiv("loading_popup");void 0!==e&&e.CloseFloatingControl()}function displayWaitPopup(e){var t=AddNewFloatingDiv("wait_popup"),n='<div style="width:200px; text-align:center; overflow:hidden;">'+txt.ajax_wait_msg+"</div>";(new Image).src="Images/loading.gif",n+='<img src="Images/loading.gif" style="margin-left:90px; margin-top:10px;" />',t.setContents(n),t.hideCloseControl();var i=new Array;i.push({value:"Cancel",jsCall:"",class:"basic_link_csr_js"}),t.setButtons(i),t.display(),Event.observe("button_0","click",(function(t){t.stop(),e.transport.abort(),e.options.onFailure&&e.options.onFailure(e.transport,e.json)}))}function ToggleCheckAll(e,t){for(var n=0,i=document.forms[0].length;n<i;n++)"checkbox"==document.forms[0].elements[n].type&&document.forms[0].elements[n].id==e&&(document.forms[0].elements[n].checked=t)}function ToggleCheckAllClass(e,t){t?jQuery("."+e).attr("checked","checked"):jQuery("."+e).removeAttr("checked")}function checkForEnter(){return"13"==event.keyCode}function SelectCodeOnEnter(e){checkForEnter()&&GlobalCodeSelectControls[e].makeSelection(this)}function moveUp(e){if("select"!=(e="string"==typeof e?document.getElementById(e):e).tagName.toLowerCase()&&e.length<2)return!1;for(var t=new Array,n=0;n<e.length;n++)1==e[n].selected&&(t[t.length]=n);for(n in t)if(0!=t[n]&&e[t[n]-1]){var i=new Array(document.body.innerHTML?e[t[n]-1].innerHTML:e[t[n]-1].text,e[t[n]-1].value,e[t[n]-1].style.color);document.body.innerHTML?e[t[n]-1].innerHTML=e[t[n]].innerHTML:e[t[n]-1].text=e[t[n]].text,e[t[n]-1].value=e[t[n]].value,e[t[n]-1].style.color=e[t[n]].style.color,document.body.innerHTML?e[t[n]].innerHTML=i[0]:e[t[n]].text=i[0],e[t[n]].value=i[1],e[t[n]].style.color=i[2],e[t[n]-1].selected=!0,e[t[n]].selected=!1}reDrawPreview()}function moveDown(e){if("select"!=(e="string"==typeof e?document.getElementById(e):e).tagName.toLowerCase()&&e.length<2)return!1;for(var t=new Array,n=e.length-1;n>-1;n--)1==e[n].selected&&(t[t.length]=n);for(n in t)if(t[n]!=e.length-1&&e[t[n]+1]){var i=new Array(document.body.innerHTML?e[t[n]+1].innerHTML:e[t[n]+1].text,e[t[n]+1].value,e[t[n]+1].style.color);document.body.innerHTML?e[t[n]+1].innerHTML=e[t[n]].innerHTML:e[t[n]+1].text=e[t[n]].text,e[t[n]+1].value=e[t[n]].value,e[t[n]+1].style.color=e[t[n]].style.color,document.body.innerHTML?e[t[n]].innerHTML=i[0]:e[t[n]].text=i[0],e[t[n]].value=i[1],e[t[n]].style.color=i[2],e[t[n]+1].selected=!0,e[t[n]].selected=!1}reDrawPreview()}function DivAlert(e,t,n,i){var r=PopupDivs.length;if(AlertWindow=AddNewFloatingDiv("alert"+r),t="<div>"+t+"</div>",AlertWindow.setContents(t),AlertWindow.setTitle(e),AlertWindow.maxWidth=300,0==n.length)n.push({value:"OK",class:"alert"+r});else for(var a=0;a<n.length;a++)n[a].jsCall||(n[a].class="alert"+r);AlertWindow.setButtons(n),i&&(AlertWindow.OnCloseAction=i),AlertWindow.display()}function reDrawPreview(){for(var e=jQuery("#columns_list2 option"),t='<table id="listing_preview_table" class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0"><tr name="element_section_row" id="element_section_row">',n=0;n<e.length;n++){var i=0;jQuery("#col_width_"+e[n].value).val()&&(i=parseFloat(jQuery("#col_width_"+e[n].value).val())),t+='<th id="col_heading_'+e[n].value+'" class="windowbg" width="'+i+'%"><b>'+e[n].text+"</b></th>"}t+="</tr><tr>";for(n=0;n<e.length;n++){i=0;jQuery("#col_width_"+e[n].value).val()&&(i=parseFloat(jQuery("#col_width_"+e[n].value).val())),t+='<td class="windowbg" style="text-align:center"><input id="col_width_'+e[n].value+'" name="col_width_'+e[n].value+'" type="string" size="2" maxlength="3" value="'+i+'" class="listing_design_csr" data="col_heading_'+e[n].value+'" ></td>'}t+="</tr></table>",jQuery("#listing_preview_table").remove(),jQuery("#listing_preview_table_wrapper").append(t)}function formatNhsNo(e){if(0!=e.length&&"undefined"!=e.val()){var t=e.val().replace(/\s/g,""),n=t.substr(0,3);t.length>3&&(n+=" "+t.substr(3,3)),t.length>6&&(n+=" "+t.substr(6,4)),e.val(n)}}function IsPositiveInteger(e,t,n){var i,r;if(window.event)i=window.event.keyCode;else{if(!t)return!0;i=t.which}return r=String.fromCharCode(i),null==i||0==i||8==i||9==i||13==i||27==i||"**********".indexOf(r)>-1}function SendTo(e,t){if("undefined"!=typeof token&&token.length>0&&!(e.search(".htm")>0)){var n=e.split("#"),i=n[0].indexOf("?")>-1;n[0].length>0&&(n[0]=n[0]+(i?"&":"?")+"token="+token),e=n.length>1?n.join("#"):n.join()}t?window.open(e,t):window.location.href=e}function editTextArea(e,t){var n=t?"spellcheck":"";jQuery("#"+e).attr({title:"",class:n}).show(),initialiseSpellchecker(),jQuery("#"+e+"_editlink, #"+e+"_readonly").hide()}function DeleteFormDesignSection(e){jQuery("#EXTRASECTIONS-"+e+"-"+e).val(""),submitClicked=!0,document.forms[0].btnSaveDesign.click()}function addNewFormDesignSection(e){var t=document.createElement("input");t.type="hidden",t.id="EXTRASECTIONS-"+e+"-"+e,t.name="EXTRASECTIONS-"+e+"-"+e,t.value="section id",document.forms[0].appendChild(t),submitClicked=!0,document.forms[0].btnSaveDesign.click()}function getFieldForBatchUpdate(e,t,n){var i=null;jQuery.getJSON(scripturl+"?service=batchupdate&event=getfieldhtml_json&module="+e+"&suffix="+n+"&field="+jQuery("#"+t).val(),(function(e){i=SplitJSAndHTML(e.html_old.toString()),jQuery("#old_field_select_wrapper_"+n).html(i),i=SplitJSAndHTML(e.html_new.toString()),jQuery("#new_field_select_wrapper_"+n).html(i),AppendJavascript(e.js),RunGlobalJavascript()}))}function AddAnotherBatchUpdateRow(e,t){jQuery.getJSON(scripturl+"?service=batchupdate&event=getbatchupdaterow_json&module="+e+"&suffix="+t,(function(e){var t=SplitJSAndHTML(e.html.toString());jQuery("#batch_update_table tr:last").before(t),AppendJavascript(e.js),RunGlobalJavascript()}))}function getSavedQueryClauseForTrigger(e){new Ajax.Request(scripturl+"?action=httprequest&type=getquerywhere&responsetype=json&recordid="+e,{method:"get",onSuccess:function(e){var t=e.responseText.evalJSON();jQuery("#trg_expression_row > div.field_input_div").html(t.where+'<input type="hidden" name="trg_expression" value="'+t.where+'" /><input type="hidden" name="CHANGED-trg_expression" id="CHANGED-trg_expression" value="" />')},onFailure:function(){alert("Error retrieving query...")}})}function checkBatchUpdateInfo(){return""!=jQuery("#field_select").val()||(OldAlert("You need to choose a field to update"),!1)}function addJavascriptFile(e,t){var n=document.getElementsByTagName(t)[0],i=document.createElement("script");i.setAttribute("type","text/javascript"),i.setAttribute("src",e),n.appendChild(i)}function ClearReadOnlyField(e){jQuery("#"+e+"_title").text(""),jQuery("#"+e).val(""),FieldChanged(e)}function SetSessionValue(e,t){jQuery.ajax({url:scripturl+"?action=httprequest&type=setsession&var="+e+"&value="+t,type:"POST"})}function SetTimezoneValue(e){jQuery.ajax({url:scripturl+"?action=httprequest&type=setsession&var=Timezone&value="+(new Date).getTimezoneOffset()/60,type:"POST"}).done((function(){window.location.reload()}))}function UnlockRecord(e,t){jQuery.ajax({url:scripturl+"?action=httprequest&type=unlockrecord&table="+e+"&link_id="+t,type:"POST",async:!1})}function getQueryStringParm(e){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var t=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(window.location.href);return null==t?"":decodeURIComponent(t[1].replace(/\+/g," "))}function jqescape(e){return e.replace(/(\/)/g,"\\$1")}function popitup(e){0!=e.length&&(e.match("http://")||e.match("https://")||(e="http://"+e),SendTo(e,"_blank"))}function setQueryStringParam(e,t){var n=window.location.href,i=new RegExp("(&|\\?)("+e+"=)[a-z]+(&|$)","i");n.match(i)?n=n.replace(i,"$1$2"+t+"$3"):n+="&module="+t;var r=validateAndSanitizeUrl(n);window.location=r}function validateAndSanitizeUrl(e){return e.match(/^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i)?encodeURI(e):null}function replaceQueryParam(e,t){var n=window.location.href,i=new RegExp("([?;&])"+e+"[^&;]*[;&]?"),r=n.replace(i,"$1").replace(/&$/,""),a=validateAndSanitizeUrl((r.length>2?r+"&":"?")+(t?e+"="+t:""));window.location=a}function disableDashboardOptions(e){jQuery("#dashboard_options_"+e).attr("disabled","disabled"),setTimeout("enableDashboardOptions('"+e+"')",2e3)}function enableDashboardOptions(e){jQuery("#dashboard_options_"+e).removeAttr("disabled")}function validateSteps(){var e=null,t=new Array,n=null;return!((e=jQuery("input[name^=acs_order]"))&&(e.each((function(e,n){t.push(n.value)})),t.length>0))||(!0!==findDuplicatesInArray(t)||(n=txt.validate_step_numbers_error_msg,alert(n),!1))}function getListingCheckboxStateString(){return urlstring="",""!=jQuery("#listing_checkbox_select_all").val()&&(urlstring+="&listing_checkbox_select_all="+jQuery("#listing_checkbox_select_all").val()),jQuery(".listing_checkbox").each((function(e,t){urlstring+="&flags["+t.id.replace("listing_checkbox_","")+"]=",t.checked?urlstring+="1":urlstring+="0"})),urlstring}function getCheckedCheckboxesValuesAsString(e,t){t=void 0!==t?t:" ";var n=[];return jQuery("input[name='"+e+"[]']:checked").each((function(){n.push(jQuery(this).val())})),n.join(t)}function updateFieldWithCheckboxesValues(e){var t=getCheckedCheckboxesValuesAsString(e+"_options"," ");jQuery("#"+e).val(t)}function PromptForAdmYear(){jQuery.ajax({url:scripturl+"?action=httprequest&type=getadmyearprompttext",success:function(e){var t=SplitJSAndHTML(e);AlertWindow=AddNewFloatingDiv("PromptAdmYear"),AlertWindow.setContents(t),AlertWindow.setTitle("Choose starting assessment period"),AlertWindow.setWidth("500px");new Array;AlertWindow.setHTMLSection("floating_window_buttons",'<input type="button" id="btnAdmYearOkay" value="Okay"/><input type="button" id="btnAdmYearCancel" value="Cancel"/>'),AlertWindow.display(),RunGlobalJavascript()}})}function findDuplicatesInArray(e){for(var t=e.length,n=[],i={},r=0;r<t;r++){i[a=e[r]];i[a]=i[a]>=1?i[a]+1:1}for(var a in i)i[a]>1&&n.push(a);return 0!=n.length}function changeLock(e,t,n){var i="Images/icons/lock.png",r=txt.locked,a=1;for(var o in"1"==jQuery("#"+e).val()&&(i="Images/icons/lock_open.png",r=txt.unlocked,a=0),jQuery("#"+e).val(a),jQuery("#"+e+"-img").attr({src:i,alt:r,title:r}),n)jQuery("#"+n[o]).val(0),jQuery("#"+n[o]+"-img").attr({src:i,alt:r,title:r})}function dnChooserPopup(e,t,n){mywindow=open("index.php","myname","resizable,width=600,height=370,scrollbars=1"),mywindow.location.href=n?scripturl+"?action=browsedomain&id="+n+"&form_element="+e+"&rdn="+t+"&token="+token:scripturl+"?action=browsedomain&form_element="+e+"&rdn="+t+"&token="+token,null==mywindow.opener&&(mywindow.opener=self)}function RemoveDisabledFields(){return jQuery("input.is_disabled, textarea.is_disabled, select.is_disabled").each((function(e){jQuery(this).remove()})),!0}function addInputHidden(e){jQuery("#"+e).length<=0&&jQuery("<input>").attr({type:"hidden",id:e,name:e}).appendTo("form")}function setSectionsExpanded(){jQuery(".toggle-trigger").closest(".toggle-group").each((function(){var e=jQuery(this),t=e.find(".toggle-target").first();if(t.data("rendered")&&t.is(":visible")){var n=e.attr("id");jQuery("#SECTIONEXPANDED-"+n+"-"+n).val("1")}}))}function copyBtnOnClick(e){var t;t=e*jQuery("#copies").val(),confirm("This process will create "+t+" new records. Are you sure you want to create these copies?")&&(displayLoadPopup(),document.forms[0].rbWhat.value="copy",document.forms[0].submit())}function createCalendar(e,t,n,i,r){!globals.deviceDetect.isTablet||"date"==e.attr("type")&&!Modernizr.inputtypes.date?"Y"==t?e.each((function(){$elem=jQuery(this),$elem.datepicker({dateFormat:n,firstDay:parseInt(i)>0?parseInt(i)-1:6,showOtherMonths:!0,selectOtherMonths:!0,showButtonPanel:!0,constrainInput:!1,changeMonth:!0,yearRange:"1900:+10",changeYear:!0,maxDate:$elem.data("not-future")?0:null,beforeShowDay:function(e){weekendDays=[(parseInt(r)+5)%7,(parseInt(r)+6)%7];var t=!1;return jQuery.inArray(e.getDay(),weekendDays)>-1&&(t=!0),[!0,t?"highlight-weekend":""]}})})):e.each((function(){$elem=jQuery(this);const e=$elem.is(":disabled")?"Images/calendar-disabled.png":"Images/calendar-default.png";$elem.datepicker({showOn:"button",buttonImage:e,buttonImageOnly:!0,buttonText:"Select date",dateFormat:n,firstDay:parseInt(i)>0?parseInt(i)-1:6,showOtherMonths:!0,selectOtherMonths:!0,showButtonPanel:!0,constrainInput:!1,changeMonth:!0,yearRange:"1900:+10",changeYear:!0,maxDate:$elem.data("not-future")?0:null,beforeShowDay:function(e){weekendDays=[(parseInt(r)+5)%7,(parseInt(r)+6)%7];var t=!1;return jQuery.inArray(e.getDay(),weekendDays)>-1&&(t=!0),[!0,t?"highlight-weekend":""]}})})):jQuery(".field_date_format").hide()}function getCheckboxByName(e){return jQuery('input[name="'+e+'_options[]"]:checkbox')}function getCheckboxByNameAndValue(e,t){return jQuery('input[name="'+e+'_options[]"][value='+t+"]:checkbox")}function destroyQtips(){if(qTips.length){for(x in qTips)for(var e=0;e<qTips[x].length;e++)qTips[x][e].destroy();jQuery(".qtip").remove()}}function initialiseSpellchecker(){globals.WEB_SPELLCHECKER&&!globals.deviceDetect.isTablet&&(document.write=function(e){spellcheckButton=e},document.writeln=function(e){spellcheckButton=e},jQuery(".spellcheck").filter(":visible").each((function(){var e=jQuery(this),t=e.attr("id");e.siblings(".spellcheck-icon").length||(spellcheck[t]=new LiveSpellInstance,spellcheck[t].ServerModel="php",spellcheck[t].Fields=t,spellcheck[t].DrawSpellImageButton(!1,"../../Images/icon_spellcheck.png","../../Images/icon_spellcheck.png","Open spellchecker","spellcheck-icon"),e.after(spellcheckButton))})))}function initialiseCalendars(){var e="object"===jQuery.type(globals)&&!jQuery.isEmptyObject(globals),t=globals.calendarOnclick&&globals.dateFormat&&void 0!==globals.weekStartDay&&void 0!==globals.calendarWeekend;e&&t?createCalendar(jQuery(".date"),globals.calendarOnclick,globals.dateFormat,globals.weekStartDay,globals.calendarWeekend):"function"==typeof doCreateCalendar&&doCreateCalendar()}function unformatCurrency(e){if(e){var t=e.match(/(.+?)(\.[0-9]{1,2})?$/),n=void 0!==t[2]?t[2]:"",i=new RegExp("[^0-9.-]",["g"]),r=parseInt(t[1].replace(i,""),10)||0;return parseFloat(r+n)}return 0}function formatCurrency(e){return globals.currencyChar+e.toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function setDefaultParameter(e,t){return void 0!==e?e:t}function outputFormDataObject(e){var t={};return jQuery(".codefield").each((function(){var n=jQuery(this),i=!1,r=n.attr("id").substring(0,n.attr("id").length-6);if(0==(a=n.closest(".field_input_div").find("#"+r)).length&&"Search"==e){var a=n.closest(".field_input_div").find('input[name="'+r+'"]');i=!0}if(a.attr("name")&&"[]"===a.attr("name").substring(a.attr("name").length-2))var o=a.attr("name").substring(0,a.attr("name").length-2);else o=a.attr("name");if(jQuery.isArray(a.val()))var l=a.val().join(" ");else l=a.val();if(0!=a.length&&i&&-1!==a.val().indexOf("|"))l=a.val().replace(/\|/g," ");o&&(t[o]=l)})),jQuery("input:checkbox ,input:radio").each((function(){var e=jQuery(this);e.is(":checked")&&(t[e.attr("name")]=e.val())})),{form_data:t}}function openRiskAssurance(e,t){if(CheckChange()){$("assurance_framework_clicked").value="1",selectAllMultiple(document.getElementById("ram_objectives")),document.RAMform.rbWhat.value=e;var n=document.createElement("iframe");n.style.display="none",n.name="submitForm",document.body.appendChild(n),RAMform.target="submitForm",document.RAMform.submit(),SendTo(t)}}String.prototype.contains=function(e){return this.indexOf(e)>=0},OldAlert=window.alert,window.alert=new Function("divAlertFunc()"),divAlertFunc=function(e){document.body?DivAlert(txt.alert_title,e,new Array,null):OldAlert(e)},window.alert=divAlertFunc,"undefined"==typeof console&&(window.console={log:function(){}}),jQuery.browser={},jQuery.browser.msie=!1,jQuery.browser.version=0,navigator.userAgent.match(/MSIE ([0-9]+)\./)&&(jQuery.browser.msie=!0,jQuery.browser.version=RegExp.$1);var checkMultiples=!1,noAlertData={};function AddtoDynamicScript(e,t){jQuery("#"+e).remove();const n=document.createElement("script");n.setAttribute("type","module"),n.setAttribute("id",e),n.setAttribute("nonce",globals.NONCE),n.text=t,jQuery(n).appendTo("head")}function filterAvailableYear(){document.getElementById("ko41Year_title").value.split("/")[0]>=2022?(document.getElementById("ko41Quarter_title").style.display="none",document.getElementById("img_add_ko41Quarter").style.display="none",document.getElementById("ko41_export_quarter_text").style.display="none"):(document.getElementById("ko41Quarter_title").style.display="block",document.getElementById("img_add_ko41Quarter").style.display="inline-block",document.getElementById("ko41_export_quarter_text").style.display="inline-block")}function insertTextAtCursor(e,t){if(e)if("TEXTAREA"===e.tagName||"INPUT"===e.tagName&&"text"===e.type){const n=e.selectionStart,i=e.selectionEnd,r=e.value;e.value=r.substring(0,n)+t+r.substring(i),e.selectionStart=e.selectionEnd=n+t.length,e.dispatchEvent(new Event("input",{bubbles:!0}))}else if(e.isContentEditable){const e=window.getSelection();if(!e.rangeCount)return;const n=e.getRangeAt(0);n.deleteContents(),n.insertNode(document.createTextNode(t)),n.collapse(!1)}}function getLfpse(e){if(("TEXTAREA"===e.tagName||"INPUT"===e.tagName)&&e.id){let t=e.id.toLowerCase();if(t.includes("lfpse")||t.includes("inc_notes")||t.includes("inc_actiontaken"))return"true"}return"false"}function updatePasteBehavior(){setTimeout((function(){var e=getValuesArray("lfpse_record_lvl_one"),t=Array.isArray(e)&&e.includes("Y");jQuery("textarea").off("paste").on("paste",(function(e){var n=getLfpse(e.target);if(t&&"true"===n){var i=getClipboardText(e);if(containsHTML(i)){e.preventDefault(),showPasteAlert(jQuery(this));var r=cleanHTMLContent(i);insertTextAtCursor(e.target,r)}}}))}),100)}function getClipboardText(e){var t=e.originalEvent.clipboardData||window.clipboardData;return t.getData("text/plain")||t.getData("text")}function containsHTML(e){return/<\/?[a-z][^>]*>|<![^>]*>|<\?[^>]*\?>/gi.test(e)||/<!--[\s\S]*?-->/g.test(e)}function cleanHTMLContent(e){return e=(e=(e=e.replace(/<a\s+href=["'](.*?)["'][^>]*>(.*?)<\/a>/gi,(function(e,t,n){return n.trim()+" "+t.trim()}))).replace(/<!--[\s\S]*?-->/g,"")).replace(/<\/?[a-z][^>]*>|<![^>]*>|<\?[^>]*\?>/gi,"")}function showPasteAlert(e){e.data("alertShown")||(alert("Pasting formatted text is not allowed. Please paste as plain text."),e.data("alertShown",!0),setTimeout((function(){e.removeData("alertShown")}),1e3))}function onKeyUpBasicLinkCsr(e){slideUpAllSubnavs()}function onKeyDownBasicLinkCsr(e){if(hideAllSubnavs(),"Enter"===e.key){var t=jQuery(this).attr("url");CheckChange()&&SendTo(t)}if("Tab"===e.key&&!e.shiftKey){const e=jQuery(this).attr("tabindex"),t=jQuery(this).siblings(".nav-arrow").first();e&&t.length&&(t.attr("tabindex",e),setTimeout((function(){t.focus()}),0))}}function onKeyDownNavArrow(e){if("Enter"===e.key){const t=jQuery(this).closest("li").children("ul.subnav").first();t.length&&showSubmenu(t),e.preventDefault()}}function onClickNavArrow(e){const t=jQuery(this).closest("li").children("ul.subnav").first();t.length&&showSubmenu(t),e.preventDefault()}function onKeyDownLogoutNonce(e){if("Enter"===e.key&&CheckChange()){SendTo(window.location.origin+window.location.pathname+"?action=logout")}}function hideAllSubnavs(){jQuery(".subnav").hide()}function slideUpAllSubnavs(){jQuery(".subnav").slideUp(300)}function showSubmenu(e){hideAllSubnavs(),e.slideDown(300)}function genericCustomDropdownEvents(){var e=document.querySelector(".generic_dropdown_box"),t=e.querySelector(".generic_dropdown_box .selected-option"),n=e.querySelector(".generic_dropdown_box input[type='hidden']"),i=e.querySelectorAll(".generic_dropdown_box .custom-options li"),r=-1;function a(e){var r=e.getAttribute("data-value"),a=e.textContent;t.textContent=a,n.value=r,"range"!=r&&""!=r&&"function"==typeof isSubmitFunc&&isSubmitFunc(),Array.prototype.forEach.call(i,(function(e){e.classList.remove("selected")})),e.classList.add("selected")}function o(e,t){l(),e[t].focus(),e[t].classList.add("focus"),e[t].scrollIntoView({block:"nearest"})}function l(){Array.prototype.forEach.call(i,(function(e){e.classList.remove("focus")}))}Array.prototype.forEach.call(i,(function(e,t){e.setAttribute("tabindex","0"),e.classList.contains("selected")&&(r=t)})),e.addEventListener("click",(function(t){e.classList.toggle("open")})),e.addEventListener("keydown",(function(t){if(("Enter"===t.key||" "===t.key)&&!e.classList.contains("open"))return t.preventDefault(),void e.classList.add("open");e.classList.contains("open")&&("ArrowDown"===t.key&&(t.preventDefault(),r=(r+1)%i.length,o(i,r)),"ArrowUp"===t.key&&(t.preventDefault(),r=(r-1+i.length)%i.length,o(i,r)),"Escape"===t.key&&e.classList.remove("open"),"Enter"===t.key&&r>=0&&(t.preventDefault(),a(i[r]),e.classList.remove("open")))})),Array.prototype.forEach.call(i,(function(t,n){t.addEventListener("click",(function(n){n.stopPropagation(),a(t),e.classList.remove("open")})),t.addEventListener("focus",(function(){l(),this.classList.add("focus"),r=n})),t.addEventListener("blur",(function(){this.classList.remove("focus")})),t.addEventListener("mousedown",(function(){t.classList.add("custom-option-li-active")})),t.addEventListener("mouseup",(function(){setTimeout((function(){t.classList.remove("custom-option-li-active")}),100)}))})),document.addEventListener("click",(function(t){e.contains(t.target)||e.classList.remove("open")})),document.addEventListener("keydown",(function(t){"Escape"===t.key&&e.classList.remove("open")}))}function showInvalidOptions(e,t){if(e){var n="index.php?action=httprequest&type=getinvalidoptions&field="+e;jQuery.ajax({url:n,type:"POST",dataType:"json",success:function(n){var i=t,r=n.filter((function(e){return-1!==i.indexOf(e.code)})).map((function(e){return e.description}));if(r.length>0){var a=jQuery('label[for="'+e+'_title"]'),o=a.find(".field_extra_text");jQuery("#"+e).val("").trigger("change");var l='<div class="field_invaid_option">'+(" This option ("+r.join(", ")+") is not valid for the latest LFPSE taxonomy. Please select another option")+"</div>";o.length?o.before(l):a.append(l)}}})}}jQuery((function(){jQuery("body").on("click",".assuranceApplyChanges_csr",(function(e){e.preventDefault(),jQuery("#btnSaveApprove").hasClass("dissabled")||(jQuery("#btnSaveApprove").addClass("dissabled"),document.frmAssurance.rbWhat.value="ApplyChanges",submitClicked=!0,jQuery("form[name='frmAssurance']").submit())})).on("click",".dashborad-tabs-click-csr",(function(e){e.preventDefault();var t=jQuery(this).attr("recordid");DashboardObjects[t].SetAsCurrent()})).on("click",".dashboard_tab_x",(function(e){e.preventDefault();var t=jQuery(this).attr("recordid");DashboardObjects[t].Delete()})).on("click",".dropdown_button_csr",(function(t){t.preventDefault();var n=jQuery(this).attr("drop_down_val");jQuery("#"+n+"_title").dropdownButton(),"undefined"!=typeof getcodesCall&&getcodesCall.registerListener((function(t){if(""!=n&&"undefined"!=typeof LfpseNoSelectRules&&"undefined"!=LfpseNoSelectRules[n]&&1==psimsEnable){var i=LfpseNoSelectRules[n],r=jQuery.map(jQuery("#"+n+" option").toArray(),(function(e){return e.value}));if(jQuery("#"+n+"_title").hasClass("multi_select")&&void 0!==i){i.forEach((function(e){-1!=jQuery.inArray(e.toString(),r)?jQuery(".datixSelect.ui-front li[thisfieldid="+n+"_title]").removeClass("multiSelect").addClass("textBlur"):r.length>0&&jQuery(".datixSelect.ui-front li[thisfieldid="+n+"_title]").each((function(t,n){jQuery(n).attr("id")==e&&jQuery(n).removeClass("multiSelect").addClass("textBlur")}))})),checkMultiples=!0;var a=jQuery("#"+n+"[multiple=multiple]").get(0);e.observe(a,{childList:!0})}}})),"undefined"!=typeof getcodesCall&&(getcodesCall.done=!1)})).on("click",".mutlilistbox_button_csr",(function(e){e.preventDefault();var t=jQuery(this).attr("drop_down_val");jQuery("#"+t+"_title").deleteSelectedItems()})).on("focus",".codefield.ff_select, .codefield.multi_select",(function(e){initDropdown(jQuery(this))})).on("mouseover",".datixSelectOption.singleSelect, .datixSelectOption.multiSelect",(function(e){$this=jQuery(this),jQuery("#"+$this.attr("thisFieldId")).activate($this)})).on("mouseout",".datixSelectOption.singleSelect",(function(e){$this=jQuery(this),jQuery("#"+$this.attr("thisFieldId")).deactivate()})).on("mousedown",".datixSelectOption.singleSelect",(function(e){$this=jQuery(this),jQuery("#"+$this.attr("thisFieldId")).selectItem($this)})).on("click",".datixSelectOption.singleSelect, .datixSelectOption.multiSelect",(function(e){$this=jQuery(this),jQuery("#"+$this.attr("thisFieldId")).selectItem($this);var t=jQuery(this).attr("thisFieldId").replace("_title","");if(!$this.closest("div").hasClass("multiSelectList")&&$this.hasClass("multiSelect")&&"undefined"!=typeof LfpseNoSelectRules&&"undefined"!=LfpseNoSelectRules[t]&&1==psimsEnable){var n=LfpseNoSelectRules[t];jQuery.inArray(parseInt($this.attr("id")),n)>=0&&($this.hasClass("datixSelectOption-selected")?(jQuery.each($this.siblings(),(function(){jQuery("#"+jQuery(this).attr("thisFieldId")).deSelectItem(jQuery(this))})),$this.siblings().removeClass("multiSelect"),$this.siblings().addClass("textBlur"),$this.siblings().removeClass("datixSelectOption-selected")):($this.siblings().addClass("multiSelect"),$this.siblings().removeClass("textBlur")))}})).on("dblclick",".datixSelectOption.multiSelect",(function(e){$this=jQuery(this);var t=jQuery("#"+$this.attr("thisFieldId"));t.selectItem($this),t.addSelectedItems($this)})).on("mousedown",".datixSelectOption.multiSelect",(function(e){$this=jQuery(this);var t=jQuery("#"+$this.attr("thisFieldId"));t.setFirstSelected($this),t.selectItem($this)})).on("mouseout",".datixSelectOption.multiSelect",(function(e){$this=jQuery(this),jQuery("#"+$this.attr("thisFieldId")).deactivate($this)})).on("click",".floating_windows_close",(function(){var e=GetFloatingDiv(jQuery(this).attr("thisId"));void 0!==e&&e.CloseFloatingControl(),hideLoadPopup(),enableAllButtons(),destroyQtips()})).on("click",".floating_windows_cancel",(function(){var e=GetFloatingDiv("exporttopdfoptions");void 0!==e&&e.CloseFloatingControl()})),jQuery(document).on("click",".clear_section",(function(){var e=jQuery(this).attr("extra_attrs").split(",");ReplaceSection.apply(null,e)})),jQuery('input[value="Generate"]').on("click",(function(){confirm("Are you sure you want to generate from the selected record?")&&(displayLoadPopup(),document.forms[0].rbWhat.value="generate",document.forms[0].submit())})),jQuery("body").on("click",".save_report_prompt",(function(){var e=jQuery(this);SaveReportPrompt(e.attr("dashboard"),e.attr("module"),e.attr("as_new"),e.attr("rep_id"))})).on("click",".basic_link_csr",(function(e){var t=jQuery(this).attr("url");CheckChange()&&SendTo(t)})).on("keyup",".basic_link_csr",onKeyUpBasicLinkCsr).on("keydown",".basic_link_csr",onKeyDownBasicLinkCsr).on("keydown",".nav-arrow",onKeyDownNavArrow).on("click",".nav-arrow",onClickNavArrow).on("keydown","#logout_nonce",onKeyDownLogoutNonce).on("click",".basic_link_confirm_csr",(function(e){var t=jQuery(this).attr("url"),n=jQuery(this).attr("message");confirm(n)&&SendTo(t)})).on("click",".basic_link_confirm_submmit_csr",(function(e){jQuery(this).attr("url");var t=jQuery(this).attr("thisValue"),n=jQuery(this).attr("message");confirm(n)&&(document.forms[1].form_action.value="delete","editdoctemplate"==document.forms[1].name?document.forms[1].tem_doc_id.value=t:document.forms[1].rep_id.value=t,document.forms[1].submit())})).on("click",".basic_link_target_csr",(function(e){var t=jQuery(this).attr("url"),n=jQuery(this).attr("target_csr");CheckChange()&&SendTo(t,n)})).on("click",".basic_link_csr_js",(function(e){AddtoDynamicScript("dynamicScript_click",jQuery(this).attr("jsCall"))})).on("click",".basic_link_redirect_csr",(function(e){var t=jQuery(this).attr("url");CheckChange()&&(document.frmProfile.redirect_url.value=t,selectAllMultiCodes(),document.forms[0].submit())})).on("click",".sec_basic_link_csr",(function(e){var t=jQuery(this).attr("url");CheckChange()&&(document.forms[0].redirect_url.value=t,selectAllMultiCodes(),document.forms[0].submit())})).on("click",".basic_link_send_csr",(function(e){SendTo(jQuery(this).attr("url"))})).on("click",".basic_link_send_2_csr",(function(e){var t=jQuery(this).attr("url1"),n=jQuery(this).attr("url2"),i=jQuery(this).attr("target_csr");SendTo(t),SendTo(n,i)})).on("click","[class^='alert']",(function(e){GetFloatingDiv(jQuery(this).attr("class")).CloseFloatingControl()})).on("click",".rbWhat_csr",(function(e){var t=jQuery(this).attr("thisAction");this.form.rbWhat.value=t,this.form.submit()})).on("click",".rbWhat_saved_query_cancel_csr",(function(e){var t=jQuery(this).attr("thisAction"),n=jQuery(this).attr("message");this.form.rbWhat.value=t,confirm(n)&&this.form.submit()})).on("click",".rbWhat_saved_query_delete_csr",(function(e){confirmDelete()})).on("click",".left_menu_csr",(function(e){var t=jQuery(this).attr("panelname");e.preventDefault(),showFormPanel("panel-"+t),jQuery(".list-item").removeClass("selected"),jQuery("#menu-"+t).addClass("selected")})).on("click",".checkbox_csr",(function(e){var t=jQuery(this).attr("thisName");updateFieldWithCheckboxesValues(t),FieldChanged(t)})).on("click","#feedback_btn",(function(e){var t=jQuery(this).attr("data").split("-");SendFeedback(t[0],t[1])})).on("click",".security_group_submit",(function(e){var t=jQuery(this).attr("module");selectAllMultiple(document.getElementById("ldap_dn")),document.frmEditGroup.user_action.value="search",document.frmEditGroup.search_module.value=t})).on("click",".security_group_button",(function(e){var t=jQuery(this).attr("module");document.getElementById("permissionwhere_"+t).value=document.getElementById("searchwhere_"+t).value})).on("click","#assignAssessments",(function(e){var t=jQuery(this).attr("module"),n=jQuery(this).attr("url");displayLoadPopup(),jQuery.ajax({url:scripturl+"?action=httprequest&type=setsessionflagstate&module="+t+getListingCheckboxStateString(),type:"GET"}).done((function(){SendTo(n)}))})),jQuery("body").on("click",".D_csr",(function(){var e=jQuery(this).attr("name");onclick=popupwin=window.open("calendar.php?field="+e,"cal","dependent=yes, width=190, height=250, screenX=200, screenY=300, titlebar=yes"),popupwin.focus(),setChanged(e)})).on("change",".M_csr",(function(){setChanged(jQuery(this).attr("name"))})).on("change",".S_csr",(function(){setChanged(jQuery(this).attr("name"))})).on("change",".Y_csr",(function(){setChanged(jQuery(this).attr("name"))})).on("change",".trigger_lfpse_map_type",(function(){var e=jQuery(this).val();SendTo(scripturl+"?action=lfpsetriggermapping&type="+e)})).on("click",".Df_csr",(function(){var e=jQuery(this).attr("thisName"),t=jQuery(this).attr("data").split("-");window.open("?action=codelist&module="+t[0]+"&type="+t[1]+"&field="+t[2]+"&search=1&udf=1&value="+escape(document.getElementById(e).value),"wndCodeList","dependent,menubar=false,screenX=150,screenY=150,width=400,height=400,titlebar,scrollbars,resizable")})).on("mouseover",".Df_csr",(function(){jQuery(this).attr("src","Images/DATIX_Dropdown_n.gif")})).on("mouseout",".Df_csr",(function(){jQuery(this).attr("src","Images/CodeList16n.gif")})).on("click",".sortOrdering_csr",(function(){var e=jQuery(this).attr("cols");sortList(e)})).on("click",".all_section_menu_csr",(function(){var e=jQuery(this).attr("panel");jQuery(".list-item").removeClass("selected"),jQuery("#menu-"+e).addClass("selected")})).on("click",".delete_section",(function(){var e=jQuery(this).attr("suffix"),t=$(e);t.parentNode.removeChild(t)})).on("click",".expandIt",(function(e){expandIt(jQuery(this).attr("section"),e.target)})).on("click",".uncheckmandatory",(function(e){var t=jQuery(this).attr("section");UncheckMandatoryFields(e.target,t)})).on("change",".uncheckChange",(function(e){var t=jQuery(this).attr("name");addInputHidden(t),jQuery("input[name="+t+"]").is(":checked")?jQuery("input#"+t).val("on"):jQuery("input#"+t).val("off")})).on("click",".radio_csr",(function(e){FieldChanged(jQuery(this).attr("name"))})).on("click",".hideCheck.readonly_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("READONLY-"+e),jQuery("input[name=READONLY-"+e+"]").val("off"),jQuery("[name=READONLY-"+e+"]:checkbox").attr("checked",!1)})).on("click",".hideCheck.mandatory_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("MANDATORY-"+e),jQuery("input[name=MANDATORY-"+e+"]").val("off"),jQuery("[name=MANDATORY-"+e+"]:checkbox").attr("checked",!1)})).on("change",".hideCheck.hide_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("HIDE-"+e),jQuery("input[name=HIDE-"+e+"]").is(":checked")?jQuery("input#HIDE-"+e).val("on"):jQuery("input#HIDE-"+e).val("off")})).on("change",".readonlyCheck.readonly_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("READONLY-"+e),jQuery("input[name=READONLY-"+e+"]").is(":checked")?jQuery("input#READONLY-"+e).val("on"):jQuery("input#READONLY-"+e).val("off")})).on("click",".readonlyCheck.mandatory_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("MANDATORY-"+e),jQuery("input[name=MANDATORY-"+e+"]").val("off"),jQuery("[name=MANDATORY-"+e+"]:checkbox").attr("checked",!1)})).on("click",".readonlyCheck.hide_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("HIDE-"+e),jQuery("input[name=HIDE-"+e+"]").val("off"),jQuery("[name=HIDE-"+e+"]:checkbox").attr("checked",!1)})).on("click",".mandatoryCheck.readonly_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("READONLY-"+e),jQuery("input[name=READONLY-"+e+"]").val("off"),jQuery("[name=READONLY-"+e+"]:checkbox").attr("checked",!1)})).on("change",".mandatoryCheck.mandatory_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("MANDATORY-"+e),jQuery("input[name=MANDATORY-"+e+"]").is(":checked")?jQuery("input#MANDATORY-"+e).val("on"):jQuery("input#MANDATORY-"+e).val("off")})).on("click",".mandatoryCheck.hide_csr",(function(){var e=jQuery(this).attr("thisName");addInputHidden("HIDE-"+e),jQuery("input[name=HIDE-"+e+"]").val("off"),jQuery("[name=HIDE-"+e+"]:checkbox").attr("checked",!1)})).on("click",".delete_section_non_empty",(function(){alert("You must remove all fields from this section before it can be deleted.")})).on("click",".delete_section_empty",(function(){var e=jQuery(this).attr("NumExtraSections");confirm("Are you sure you want to remove this section?")&&DeleteFormDesignSection(e)})).on("click",".help_pop_close",(function(){GetFloatingDiv("help").CloseFloatingControl()})).on("change",".newpanelCheck",(function(){var e=jQuery(this).attr("thisName");addInputHidden("NEWPANEL-"+e),jQuery("input[name=NEWPANEL-"+e+"]").is(":checked")?jQuery("input#NEWPANEL-"+e).val("on"):jQuery("input#NEWPANEL-"+e).val("off")})).on("click",".btnApply_exsec",(function(){setReturns()&&GetFloatingDiv("expandsection").CloseFloatingControl()})).on("click",".btnCancel_exsec",(function(){GetFloatingDiv("expandsection").CloseFloatingControl()})).on("click",".btnApply_exfield",(function(){setReturns()&&GetFloatingDiv("expandfield").CloseFloatingControl()})).on("click",".btnCancel_exfield",(function(){GetFloatingDiv("expandfield").CloseFloatingControl()})).on("click",".add_new_sections",(function(){addNewFormDesignSection(jQuery(this).attr("NumExtraSections"))})).on("click",".cancel_new_field_button",(function(){GetFloatingDiv("changefieldsection").CloseFloatingControl()})).on("click",".moveOptions_csr",(function(){var e=jQuery(this).attr("extra_attrs").split(",");moveOptions.apply(null,e)})).on("keyup",".password_policy_csr",(function(){var e=jQuery(this).val();jQuery(this).val(e.replace(/[^\d]/,""))})).on("change",".listing_design_csr",(function(){var e=jQuery(this).attr("data");jQuery("#"+e).width(jQuery(this).val().toString()+"%")})).on("click",".complete_button_cancel",(function(){GetFloatingDiv("progress_popup").CloseFloatingControl()})).on("click",".check_all_checkbox_csr",(function(){ToggleCheckAllClass(jQuery(this).attr("data"),jQuery(this).get(0).checked)})).on("click","#btnAgree",(function(){GetFloatingDiv("AgreementMessage").CloseFloatingControl(),agree()})).on("click","#btnDisagree",(function(){GetFloatingDiv("AgreementMessage").CloseFloatingControl(),disagree()})).on("click","#btnAdmYearOkay",(function(){""!=jQuery("#adm_year").val()?SendTo(scripturl+"?action=list&module=LOC&creatingassignments=1&sidemenu=ACR&adm_year="+jQuery("#adm_year").val()):OldAlert("You must choose an assessment period.")})).on("click","#btnAdmYearCancel",(function(){GetFloatingDiv("PromptAdmYear").CloseFloatingControl()})).on("click",".pageing_csr",(function(){var e=jQuery(this).attr("orderby"),t=jQuery(this).attr("order"),n=jQuery(this).attr("number");pageing(e,t,n)})).on("click",".WidgetEditPopup_save",(function(){var e=jQuery(this).attr("settingsid");WidgetObjects[e].Save(),jQuery("div[id^=collorpicker_]").remove()})).on("click",".WidgetEditPopup_saveas",(function(){var e=jQuery(this).attr("settingsid");WidgetObjects[e].SaveAsNew(),jQuery("div[id^=collorpicker_]").remove()})).on("click",".WidgetEditPopup_cancel",(function(){GetFloatingDiv("WidgetEditPopup").CloseFloatingControl(),destroyQtips()})).on("click",".add_injury_csr",(function(){var e=jQuery(this).attr("extra_attrs").split(",");e.unshift(jQuery(this).get(0)),addInjury.apply(null,e)})).on("click",".add_property_csr",(function(){var e=jQuery(this).attr("extra_attrs");addProperty(jQuery(this).get(0),e,"E",mandatoryFieldsEncoded)})).on("blur",".ipp_value_blur_csr",(function(){ValidateCurrency(jQuery(this))})).on("change",".section_root_checked",(function(){var e=jQuery(this).attr("data");jQuery("#"+e).val("1")})).on("change",".section_root_unchecked",(function(){var e=jQuery(this).attr("data"),t=jQuery(this).attr("changeto");jQuery("#"+e).val("1"),setChanged(t)})).on("change",".module_listing_select",(function(){jQuery(".field_list_select").hide(),jQuery("#"+jQuery("#table_list").val()+"_field_list").show()})).on("change","#qry_name",(function(){"range"!=document.queryform.qry_name.options[document.queryform.qry_name.selectedIndex].value&&""!=document.queryform.qry_name.options[document.queryform.qry_name.selectedIndex].value&&document.queryform.submit()})).on("click",".copy_record_csr",(function(){copyBtnOnClick(jQuery(this).attr("selectedRecords"))})).on("click",".toggle_twisty_expand",(function(){var e=jQuery(this).attr("extra_attrs").split(",");ToggleTwistyExpand.apply(null,e)})).on("click",".create_code_list",(function(){CreateCodeList(jQuery(this).attr("filename"),!0)})).on("click","[class^='closethis__']",(function(e){GetFloatingDiv(jQuery(this).attr("class").split("__")[1]).CloseFloatingControl()})).on("click",".add_new_message_csr",(function(e){AddNewMessageBox(jQuery(this).attr("field"))})).on("click",".link_web",(function(e){var t=jQuery(this).attr("link");t.match("http://")||t.match("https://")||(t="http://"+t),window.open(t)})).on("click",".browse_directory",(function(){var e=jQuery(this).attr("extra_attrs").split(",");dnChooserPopup.apply(null,e)})).on("keyup",".formatNhsNo",(function(){formatNhsNo(jQuery(this))})).on("keyup",".password_length_csr",(function(){$this=jQuery(this),$this.val($this.val().replace(/[^\d]/,""))})).on("keyup",".initTyper_csr",(function(){initTyper(jQuery(this))})).on("change",".datetime_csr",(function(){setChanged(jQuery(this).attr("name"))})).on("blur",".basic_blur_csr_js",(function(){AddtoDynamicScript("dynamicScript_blur",jQuery(this).attr("jsCall"))})).on("change",".basic_change_csr_js",(function(){AddtoDynamicScript("dynamicScript_change",jQuery(this).attr("jsCall"))})).on("click",".close_pop_list_csr",(function(){window.open("","_self"),window.close()})).on("click",".choose_pop_list_csr",(function(){var e=jQuery(this).attr("url");window.opener.location.href=e,window.open("","_self"),window.close()})).on("click",".select_pop_list_csr",(function(){var e=jQuery(this).attr("url");window.opener.location.href=e,window.open("","_self"),window.close()})).on("click",".inner_cancel_csr",(function(){return confirmAndDelete()})).on("keypress",".subject_order_csr",(function(e){return IsPositiveInteger(jQuery(this).get(0),e)})).on("click",".ajax_email_click_disable",(function(){jQuery(".ajax_email_click_disable").prop("disabled",!0),feedbackCancel(),displayLoadPopup()})).on("click",".toggle_csr",(function(){toggleStep(jQuery(this))})).on("click",".automatically_merge_contacts",(function(){confirm("Are you sure you would like to proceed with this action? Please ensure you have reviewed / set the merge settings under the Admin - Configuration - Contacts section.")&&SendTo(jQuery(this).attr("jsCall"))})).off("paste").on("paste",(function(e){const t=new URLSearchParams(window.location.search).get("module");"undefined"!=typeof psimsEnable&&1==psimsEnable&&""!=t&&"INC"==t&&updatePasteBehavior()})).on("input change blur","[name=lfpse_record_lvl_one], [name=lfpse_record_lvl_two], input[type='checkbox'], input[type='radio'], select",(function(){const e=new URLSearchParams(window.location.search).get("module");"undefined"!=typeof psimsEnable&&1==psimsEnable&&""!=e&&"INC"==e&&updatePasteBehavior()}));var e=new MutationObserver((function(e){e.forEach((function(e){if(checkMultiples){if("undefined"!=typeof LfpseNoSelectRules&&"undefined"!=LfpseNoSelectRules[n]&&1==psimsEnable){var t=jQuery.map(jQuery(e.target).find("option").toArray(),(function(e){return e.value})),n=e.target.id,i=LfpseNoSelectRules[n];t=jQuery.map(jQuery("#"+n+" option").toArray(),(function(e){return e.value}));jQuery("#"+n+"_title").hasClass("multi_select")&&void 0!==i&&i.forEach((function(e){-1!=jQuery.inArray(e.toString(),t)&&t.length>i.length&&(jQuery.each(jQuery("#"+n+"_values li"),(function(){jQuery("#"+jQuery(this).attr("thisFieldId")).selectItem(jQuery(this)),e.toString()!=jQuery(this).attr("id")||alert('Disabled to select various options along with "'+jQuery(this).text()+'"')})),jQuery("#"+n+"_title").deleteSelectedItems())}))}checkMultiples=!1}}))}))})),jQuery(document).ready((function(){updatePasteBehavior()}));